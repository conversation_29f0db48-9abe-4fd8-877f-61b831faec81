{"permissions": {"allow": ["Bash(rm:*)", "Bash(go mod:*)", "Bash(go build:*)", "Bash(go generate:*)", "<PERSON><PERSON>(make:*)", "Bash(go install:*)", "Bash(find:*)", "Bash(grep:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "<PERSON><PERSON>(curl:*)", "Bash(git add:*)", "Bash(cd:*)", "Bash(git commit:*)", "Bash(go fmt:*)", "<PERSON><PERSON>(docker-compose up:*)", "<PERSON><PERSON>(docker exec:*)", "Bash(psql:*)", "Bash(npm run type-check:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "Bash(pnpm run:*)", "Bash(npx tsc:*)", "WebFetch(domain:alova.js.org)", "Ba<PERSON>(go vet:*)", "Bash(npx vue-tsc:*)", "WebFetch(domain:z-paging.zxlee.cn)"], "deny": []}}