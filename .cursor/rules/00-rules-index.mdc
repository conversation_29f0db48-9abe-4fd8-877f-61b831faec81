---
alwaysApply: true
---
# Cursor 规则索引

本目录包含项目的所有编程规范、开发指南和最佳实践文档。Cursor IDE 将自动读取这些规则以提供更好的代码建议和检查。

## 📋 规则文档列表

### 项目概览
- [01-project-overview.mdc](./01-project-overview.mdc) - 项目总体概述
- [02-project-structure.mdc](./02-project-structure.mdc) - 项目目录结构

### 前端开发
- [04-components-guide.mdc](./04-components-guide.mdc) - 组件使用指南
- [06-network-requests.mdc](./06-network-requests.mdc) - 网络请求规范
- [07-ui-ux-guidelines.mdc](./07-ui-ux-guidelines.mdc) - UI/UX 设计指南

### 后端开发
- [08-backend-development-guidelines.mdc](./08-backend-development-guidelines.mdc) - 后端开发总体指南
- [09-golang-coding-standards.mdc](./09-golang-coding-standards.mdc) - Go 编程规范 (Go 1.24)
- [10-golang-development-tools.mdc](./10-golang-development-tools.mdc) - Go 开发工具和流程

## 🎯 Go 开发规范重点

对于 Go 后端开发，请特别关注：

### 必读文档
1. **[Go 编程规范](./09-golang-coding-standards.mdc)** - 基于 Uber Go Style Guide 的完整编程规范
2. **[Go 开发工具](./10-golang-development-tools.mdc)** - 代码质量工具使用指南

### 核心工具
- `goimports` - 代码格式化和导入管理
- `go vet` - 官方静态检查
- `staticcheck` - 深度静态分析
- `gosec` - 安全漏洞扫描

### 快速开始
```bash
# 安装开发工具
cd backend && make install-tools

# 日常代码检查
cd backend && make check

# 提交前完整检查
cd backend && make check-all
```

## 🔧 IDE 集成

这些规则已配置为与 Cursor IDE 集成：

1. **自动应用规范** - Cursor 会根据这些规则提供代码建议
2. **实时检查** - 编写代码时实时应用规范检查
3. **自动格式化** - 保存时自动应用代码格式化

## 📝 更新说明

- 所有 Go 编程规范已从 `backend/` 目录移动到 `.cursor/rules/`
- IDE 可以更好地识别和应用这些规范
- 规范文档使用 `.mdc` 格式，便于 Cursor 解析

---

💡 **提示**: 这些规范旨在提高代码质量和团队协作效率。如有疑问或建议，请及时反馈。# Cursor 规则索引

本目录包含项目的所有编程规范、开发指南和最佳实践文档。Cursor IDE 将自动读取这些规则以提供更好的代码建议和检查。

## 📋 规则文档列表

### 项目概览
- [01-project-overview.mdc](./01-project-overview.mdc) - 项目总体概述
- [02-project-structure.mdc](./02-project-structure.mdc) - 项目目录结构

### 前端开发
- [04-components-guide.mdc](./04-components-guide.mdc) - 组件使用指南
- [06-network-requests.mdc](./06-network-requests.mdc) - 网络请求规范
- [07-ui-ux-guidelines.mdc](./07-ui-ux-guidelines.mdc) - UI/UX 设计指南

### 后端开发
- [08-backend-development-guidelines.mdc](./08-backend-development-guidelines.mdc) - 后端开发总体指南
- [09-golang-coding-standards.mdc](./09-golang-coding-standards.mdc) - Go 编程规范 (Go 1.24)
- [10-golang-development-tools.mdc](./10-golang-development-tools.mdc) - Go 开发工具和流程

## 🎯 Go 开发规范重点

对于 Go 后端开发，请特别关注：

### 必读文档
1. **[Go 编程规范](./09-golang-coding-standards.mdc)** - 基于 Uber Go Style Guide 的完整编程规范
2. **[Go 开发工具](./10-golang-development-tools.mdc)** - 代码质量工具使用指南

### 核心工具
- `goimports` - 代码格式化和导入管理
- `go vet` - 官方静态检查
- `staticcheck` - 深度静态分析
- `gosec` - 安全漏洞扫描

### 快速开始
```bash
# 安装开发工具
cd backend && make install-tools

# 日常代码检查
cd backend && make check

# 提交前完整检查
cd backend && make check-all
```

## 🔧 IDE 集成

这些规则已配置为与 Cursor IDE 集成：

1. **自动应用规范** - Cursor 会根据这些规则提供代码建议
2. **实时检查** - 编写代码时实时应用规范检查
3. **自动格式化** - 保存时自动应用代码格式化

## 📝 更新说明

- 所有 Go 编程规范已从 `backend/` 目录移动到 `.cursor/rules/`
- IDE 可以更好地识别和应用这些规范
- 规范文档使用 `.mdc` 格式，便于 Cursor 解析

---

💡 **提示**: 这些规范旨在提高代码质量和团队协作效率。如有疑问或建议，请及时反馈。