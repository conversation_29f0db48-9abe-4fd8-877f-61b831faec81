---
description: 项目目录结构说明
globs: 
alwaysApply: true
---

# 📁 项目目录结构

## 项目整体结构

本项目采用前后端分离架构，根目录下包含以下主要目录：

```
fnbdb-mini/
├── front/          # 前端工程目录
├── backend/        # 后端工程目录
├── .cursor/        # Cursor 编辑器配置
├── .git/           # Git 版本控制
└── 项目文档文件...
```

## 📱 前端工程目录 (front/)

**前端技术栈**：基于 uni-app CLI 框架的跨平台应用，使用 Vue 3 + TypeScript 开发

**主要结构**：
```
front/
├── src/
│   ├── pages/           # 页面组件
│   ├── components/      # 通用组件
│   ├── stores/          # Pinia 状态管理
│   ├── utils/           # 工具函数
│   ├── styles/          # 样式文件
│   ├── api/             # API 接口
│   ├── constants/       # 常量定义
│   └── types/           # TypeScript 类型定义
├── static/              # 静态资源
├── package.json         # 依赖管理
├── pages.json           # 页面配置
├── uni.scss             # 全局样式
└── vite.config.ts       # 构建配置
```

**关键文件**：
- 入口文件：`front/src/main.ts`
- 应用组件：`front/src/App.vue`
- 页面配置：`front/src/pages.json`

## 🚀 后端工程目录 (backend/)

**后端技术栈**：待定（根据具体需求选择合适的技术栈）

**预期结构**：
```
backend/
├── src/                 # 源代码目录
├── config/              # 配置文件
├── docs/                # 后端文档
├── package.json         # 依赖管理
└── 其他配置文件...
```

## 🔧 开发工作流

### 前端开发任务
执行前端相关任务时，请：
1. 切换到 `front/` 目录
2. 使用前端项目的配置和依赖
3. 遵循前端编码规范和组件设计原则

**示例命令**：
```bash
cd front/
pnpm install
pnpm run dev:mp-weixin
```

### 后端开发任务
执行后端相关任务时，请：
1. 切换到 `backend/` 目录
2. 使用后端项目的配置和依赖
3. 遵循后端 API 设计和数据库规范

**示例命令**：
```bash
cd backend/
# 根据具体技术栈执行相应命令
```

## 📋 开发规范

### 任务执行原则
1. **明确目标目录**：每次执行任务前，明确是前端还是后端任务
2. **切换工作目录**：使用正确的工作目录进行开发
3. **保持结构清晰**：前后端代码严格分离，避免混合
4. **统一代码规范**：前后端分别遵循各自的编码规范

### 文件路径引用
- 前端文件路径：`front/src/...`
- 后端文件路径：`backend/src/...`
- 共享文档：项目根目录

### 依赖管理
- 前端依赖：`front/package.json`
- 后端依赖：`backend/package.json`
- 分别独立管理，避免依赖冲突

## 🎯 注意事项

1. **工作目录切换**：执行任务时请确保在正确的子目录中
2. **文件路径**：引用文件时请使用完整的相对路径
3. **配置文件**：前后端各自维护独立的配置文件
4. **版本控制**：Git 仓库在根目录，统一管理版本控制

**记住**：前端专注于用户界面和交互体验，后端专注于数据处理和业务逻辑，保持职责分离！
