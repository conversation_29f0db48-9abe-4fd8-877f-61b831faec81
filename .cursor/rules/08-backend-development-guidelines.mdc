---
alwaysApply: true
---
- 您是一位专业的 AI 编程助理，专门使用 Go 构建 api，使用 Go 1.24、gin 框架、gorm 框架、zerolog、Postgresql、redis 等技术栈进行开发,并熟悉 API 设计原则、最佳实践和 Go 惯用法。

Follow the user's requirements carefully & to the letter.

First think step-by-step - describe your plan for the API structure, endpoints, and data flow in pseudocode, written out in great detail.

Confirm the plan, then write code!

Write correct, up-to-date, bug-free, fully functional, secure, and efficient Go code for APIs.

If unsure about a best practice or implementation detail, say so instead of guessing.

Offer suggestions for testing the API endpoints using Go's testing package.
Always prioritize security, scalability, and maintainability in your API designs and implementations.

每次创建Model时都需要在`schema.sql`中增加对应的表结构和建表sql，要注意表名和字段名，不要写错。

开发步骤：编写代码 → make check → 修复问题 → 提交

## Go 编程规范

在开发 Go 代码时，严格遵循以下规范：

📖 **[Go 编程规范 (Go 1.24)](./09-golang-coding-standards.mdc)** - 完整的编程规范指南
🛠️ **[Go 开发工具和流程](./10-golang-development-tools.mdc)** - 开发工具使用和流程指南

### 核心要求

1. **代码质量检查**：每次提交前必须运行 `cd backend && make check-all`
2. **代码格式化**：使用 `goimports` 自动格式化和管理导入
3. **静态分析**：使用 `staticcheck` 进行深度代码分析  
4. **安全检查**：使用 `gosec` 扫描安全漏洞
5. **错误处理**：严格按照规范处理错误，避免 panic

### 快速检查命令

```bash
cd backend
make check-all  # 完整代码质量检查
make test       # 运行测试
```

以下是后端项目的目录结构和开发规范

# 后端目录结构与开发规范

本文档根据 `plan/backend-directory-structure.md` 整理，旨在为后端开发提供统一的目录结构和编码规范。

## 1、核心目录结构

```
bdb-backend/
├── cmd/                          # 应用程序入口 (main.go)
├── internal/                     # 私有应用代码
│   ├── api/                     # 应用程序层 (Gin)
│   │   ├── controller/          # HTTP处理层 (Handler)
│   │   ├── router/              # 路由配置
│   │   ├── middleware/          # 中间件
│   │   └── server.go            # 服务器配置
│   ├── service/                 # 业务逻辑层
│   ├── repository/              # 数据访问层 (GORM)
│   ├── model/                   # 数据模型 (Structs)
│   ├── types/                   # API请求/响应结构体
│   └── utils/                   # 通用工具
├── pkg/                          # 公共库代码 (可被外部项目引用)
│   ├── auth/                    # 认证 (JWT, password)
│   ├── cache/                   # 缓存 (Redis)
│   ├── database/                # 数据库
│   ├── logger/                  # 日志
│   ├── validator/               # 验证
│   └── response/                # 统一响应格式
├── configs/                      # 配置文件 (config.yaml)
├── migrations/                   # 数据库迁移脚本
├── deployments/                  # 部署配置 (Dockerfile, docker-compose.yml)
├── scripts/                      # 脚本
├── tests/                        # 测试
├── docs/                         # 文档
├── go.mod                        # Go模块
└── README.md                     # 项目说明
```

## 2、分层架构

严格遵守 `controller` -> `service` -> `repository` 的分层架构。

- **`controller` (Handler)**: 解析 HTTP 请求，参数校验，调用 `service`。**禁止包含业务逻辑**。
- **`service`**: 核心业务逻辑处理。
- **`repository`**: 数据持久化操作，与数据库交互。
- **`model`**: 数据库表对应的 Go 结构体。
- **`types`**: API 的请求(Request)和响应(Response)数据结构体。

## 3、API 开发规范

3.1. **参数验证**: `controller`层必须使用 `pkg/validator` 对请求参数进行校验。
3.2. **统一响应**: 所有 API 接口必须使用 `pkg/response` 中封装的 `Success` 和 `Error` 方法返回统一的 JSON 结构。
`json
    {
        "code": 0, // 0为成功，非0为错误
        "message": "success",
        "data": {}
    }
    `
3.3. **日志记录**: - 只在关键业务流程、错误捕获、异步任务等重要位置记录日志。 - 使用 `pkg/logger` 提供的结构化日志。 - 避免在 `controller` 中记录过多常规请求日志。错误应在 `service` 或 `repository` 层捕获并向上传递。
3.4. - **使用 `context.Context`**: 在处理请求的整个生命周期中，应传递 `context.Context`，用于控制超时、取消操作和传递请求域的值（如 `request_id`）。`ctx` 应作为函数的第一个参数。
3.5 - **Goroutine 通信**: 优先使用 Channel 进行 Goroutine 之间的通信，而不是通过共享内存加锁的方式。
3.6. **分页请求规范**: 为了统一处理分页逻辑，所有需要分页的列表查询API应遵循以下规范：
    - 在 `internal/types` 中定义一个可复用的分页请求结构体 `PaginationReq`。
    - 各业务的列表查询请求结构体（如 `GetXXXListRequest`）应嵌入 `PaginationReq`。
    - 在 `controller` 层，调用 `validator.CheckQuery()` 

    ```go
    // types/common_types.go
    type PaginationReq struct {
        Page int `form:"page,default=1"`
        PageSize int `form:"pageSize,default=20"`
    }
    ```

## 4、命名约定

- **文件名**: 使用下划线命名法（`user_ctl.go`, `user_svc.go`）。
- **包名**: 使用简短、小写、单数形式的名称（`controller`, `service`）。
- **结构体/接口**: 使用驼峰命名法（`UserService`, `UserCtl`）。

## 5、代码风格

- **格式化**: 使用 `gofmt` 格式化代码。
- **注释**: 为每个文件、函数、结构体添加必要的简短注释。
- **错误处理**: 使用 `pkg/response` 中定义的错误码，避免直接返回错误信息。
- **依赖管理**: 使用 `go mod` 管理依赖。
- **测试**: 为每个功能编写单元测试，并在 CI 中运行。
- **代码规范**: 遵循 Go 语言的编码规范，使用规范命名、使用 `go fmt` 格式化代码等；避免对某个模块的代码进行过度封装和过度设计，保证代码的可读性和可维护性。

## 6. 配置与日志

### 6.1 配置管理

- **配置分离**: 使用 `Viper` 等库管理配置。将配置与代码分离，并通过配置文件（如 `config.yaml`）或环境变量加载。
- **环境隔离**: 为开发 (`dev`)、测试 (`test`)、生产 (`prod`) 环境提供不同的配置文件。
- **禁止硬编码**: 严禁在代码中硬编码任何敏感信息（如数据库密码、API Key）。

### 6.2 日志记录

- **结构化日志**:`Zerolog` 等库进行结构化日志记录 (JSON 格式)。
- **日志级别**: 正确使用日志级别 (`Debug`, `Info`, `Warn`, `Error`, `Fatal`)。生产环境一般设置为 `Info` 级别。
- **包含上下文**: 日志中应包含 `request_id`, `user_id` 等上下文信息，便于追踪和调试。
并不是所有日志都记录上下文，导致日志文件过大，影响性能。
- **业务日志记录**: 根据业务程度进行适当记录，例如上报error或者debug记录进行调试；不要无限制的在业务中记录日志。例如api验证参数、参数验证等不需要记录日志，这属于开发阶段可解决的问题。

## 7. GORM 使用与高性能 SQL

### 7.1 GORM 规范

- **模型公共字段**: 如果模型存在id、created_at、updated_at 等字段，应增加 `BaseModel` ；如果存在软删除则加入`SoftDelete`。

- **避免 `SELECT *`**: 严禁使用 `db.First(&user)` 而不指定字段。必须使用 `db.Select("id", "name", ...).First(&user)` 来明确指定所需字段，减少不必要的数据传输和内存占用。
- **N+1 问题**: 警惕并解决 N+1 查询问题。使用 `Preload` 进行预加载,如果api数据包含关联数据，务必使用Preload进行预加载。



  ```go
  // Bad: N+1 query
  db.Find(&users)
  for _, user := range users {
    db.Model(&user).Association("Profile").Find(&user.Profile)
  }

  // Good: Eager Loading
  db.Preload("Profile").Find(&users)
  ```

- **事务 (Transaction)**: 多个写操作必须放在一个事务中执行，使用 `db.Transaction` 来确保数据一致性。
- **错误检查**: GORM 的每个操作（Create, Query, Update, Delete）都必须检查返回的 `error`。特别是查询操作，需要判断 `gorm.ErrRecordNotFound`。

### 7.2 高性能 SQL 实践

- **索引 (Indexing)**:
  - **查询驱动**: 为 `WHERE` 子句中频繁用作查询条件的列建立索引。
  - **外键索引**: 所有外键列必须建立索引。
  - **组合索引**: 如果查询条件经常涉及多个列，应考虑建立组合索引。注意索引列的顺序。
  - **`EXPLAIN` 分析**: 对于复杂或慢查询，必须使用 `EXPLAIN` 分析其执行计划，找出性能瓶颈并进行优化。
- **避免大事务**: 事务应尽可能简短，只包含必要的操作，以减少锁定的时间和范围，提高并发性能。
- **批量操作**: 对于大量数据的插入或更新，使用 `CreateInBatches` 等批量操作方法。


## 8. 错误处理
### 8.1 错误处理

- **错误是值 (Errors are values)**: 函数如果可能出错，应将 `error` 作为最后一个返回值。
- **立即处理错误**: 不要使用 `_` 忽略错误。收到错误后应立即检查和处理。
- **错误包装 (Error Wrapping)**: 在调用链路中向上传递错误时，应添加上下文信息，方便调试。使用 `fmt.Errorf("...: %w", err)` 来包装错误。
- **特定错误判断**: 使用 `errors.Is()` 来判断是否为特定错误（例如 `gorm.ErrRecordNotFound`），使用 `errors.As()` 来转换到特定的错误类型。



## 9. 建表SQL规范
  - **数据库类型**: 符合Postgresql的数据类型，避免使用外键约束。
  - **表名**: 使用小写、复数形式的名称（`users`, `orders`）。
  - **字段名**: 使用小写、单数形式的名称（`user_id`, `order_id`）。
  - **字段类型**: 使用合适的数据类型（`varchar`, `int`, `timestamp`）,涉及到金额的字段使用int类型，单位为：分，更方便计算；根据不同字段职责使用不同长度，例如：用户名、昵称、手机号、邮箱等使用varchar(255)，密码使用varchar(255)，身份证号使用varchar(18)，地址使用varchar(255)，备注使用varchar(255)，其他字段根据实际情况设置。
  - **字段注释**: 为每个字段添加必要的注释，说明字段含义、约束等；在每个字段后面添加注释，例如：`user_id int comment '用户ID'`。
  - **特殊字段**: created_at、updated_at使用TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP；如果存在软删除，使用`is_del` smallint NOT NULL DEFAULT 0 COMMENT '是否删除 0否 1是'，并添加索引 `idx_is_del`。
  - **字段默认值**: 对于可为空的字段，应设置默认值（例如 `{}` 或 `''`）。
  - **字段注释**: 为每个字段添加必要的注释，说明字段含义、约束等；在每个字段后面添加注释，例如：`user_id int comment '用户ID'`。
  - **特殊字段**: created_at、updated_at使用TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP；如果存在软删除，使用`is_del` smallint NOT NULL DEFAULT 0 COMMENT '是否删除 0否 1是'，并添加索引 `idx_is_del`。
  - **字段默认值**: 对于可为空的字段，应设置默认值（例如 `{}` 或 `''`）。- 您是一位专业的 AI 编程助理，专门使用 Go 构建 api，使用 Go 1.24、gin 框架、gorm 框架、zerolog、Postgresql、redis 等技术栈进行开发,并熟悉 API 设计原则、最佳实践和 Go 惯用法。

Follow the user's requirements carefully & to the letter.

First think step-by-step - describe your plan for the API structure, endpoints, and data flow in pseudocode, written out in great detail.

Confirm the plan, then write code!

Write correct, up-to-date, bug-free, fully functional, secure, and efficient Go code for APIs.

If unsure about a best practice or implementation detail, say so instead of guessing.

Offer suggestions for testing the API endpoints using Go's testing package.
Always prioritize security, scalability, and maintainability in your API designs and implementations.

每次创建Model时都需要在`schema.sql`中增加对应的表结构和建表sql，要注意表名和字段名，不要写错。

开发步骤：编写代码 → make check → 修复问题 → 提交

## Go 编程规范

在开发 Go 代码时，严格遵循以下规范：

📖 **[Go 编程规范 (Go 1.24)](./09-golang-coding-standards.mdc)** - 完整的编程规范指南
🛠️ **[Go 开发工具和流程](./10-golang-development-tools.mdc)** - 开发工具使用和流程指南

### 核心要求

1. **代码质量检查**：每次提交前必须运行 `cd backend && make check-all`
2. **代码格式化**：使用 `goimports` 自动格式化和管理导入
3. **静态分析**：使用 `staticcheck` 进行深度代码分析  
4. **安全检查**：使用 `gosec` 扫描安全漏洞
5. **错误处理**：严格按照规范处理错误，避免 panic

### 快速检查命令

```bash
cd backend
make check-all  # 完整代码质量检查
make test       # 运行测试
```

以下是后端项目的目录结构和开发规范

# 后端目录结构与开发规范

本文档根据 `plan/backend-directory-structure.md` 整理，旨在为后端开发提供统一的目录结构和编码规范。

## 1、核心目录结构

```
bdb-backend/
├── cmd/                          # 应用程序入口 (main.go)
├── internal/                     # 私有应用代码
│   ├── api/                     # 应用程序层 (Gin)
│   │   ├── controller/          # HTTP处理层 (Handler)
│   │   ├── router/              # 路由配置
│   │   ├── middleware/          # 中间件
│   │   └── server.go            # 服务器配置
│   ├── service/                 # 业务逻辑层
│   ├── repository/              # 数据访问层 (GORM)
│   ├── model/                   # 数据模型 (Structs)
│   ├── types/                   # API请求/响应结构体
│   └── utils/                   # 通用工具
├── pkg/                          # 公共库代码 (可被外部项目引用)
│   ├── auth/                    # 认证 (JWT, password)
│   ├── cache/                   # 缓存 (Redis)
│   ├── database/                # 数据库
│   ├── logger/                  # 日志
│   ├── validator/               # 验证
│   └── response/                # 统一响应格式
├── configs/                      # 配置文件 (config.yaml)
├── migrations/                   # 数据库迁移脚本
├── deployments/                  # 部署配置 (Dockerfile, docker-compose.yml)
├── scripts/                      # 脚本
├── tests/                        # 测试
├── docs/                         # 文档
├── go.mod                        # Go模块
└── README.md                     # 项目说明
```

## 2、分层架构

严格遵守 `controller` -> `service` -> `repository` 的分层架构。

- **`controller` (Handler)**: 解析 HTTP 请求，参数校验，调用 `service`。**禁止包含业务逻辑**。
- **`service`**: 核心业务逻辑处理。
- **`repository`**: 数据持久化操作，与数据库交互。
- **`model`**: 数据库表对应的 Go 结构体。
- **`types`**: API 的请求(Request)和响应(Response)数据结构体。

## 3、API 开发规范

3.1. **参数验证**: `controller`层必须使用 `pkg/validator` 对请求参数进行校验。
3.2. **统一响应**: 所有 API 接口必须使用 `pkg/response` 中封装的 `Success` 和 `Error` 方法返回统一的 JSON 结构。
`json
    {
        "code": 0, // 0为成功，非0为错误
        "message": "success",
        "data": {}
    }
    `
3.3. **日志记录**: - 只在关键业务流程、错误捕获、异步任务等重要位置记录日志。 - 使用 `pkg/logger` 提供的结构化日志。 - 避免在 `controller` 中记录过多常规请求日志。错误应在 `service` 或 `repository` 层捕获并向上传递。
3.4. - **使用 `context.Context`**: 在处理请求的整个生命周期中，应传递 `context.Context`，用于控制超时、取消操作和传递请求域的值（如 `request_id`）。`ctx` 应作为函数的第一个参数。
3.5 - **Goroutine 通信**: 优先使用 Channel 进行 Goroutine 之间的通信，而不是通过共享内存加锁的方式。
3.6. **分页请求规范**: 为了统一处理分页逻辑，所有需要分页的列表查询API应遵循以下规范：
    - 在 `internal/types` 中定义一个可复用的分页请求结构体 `PaginationReq`。
    - 各业务的列表查询请求结构体（如 `GetXXXListRequest`）应嵌入 `PaginationReq`。
    - 在 `controller` 层，调用 `validator.CheckQuery()` 完成参数绑定和基础验证后，必须立即调用请求结构体的 `Sanitize()` 方法来设置默认值和限制范围，以确保分页参数的有效性和安全性。

    ```go
    // types/common_types.go
    type PaginationReq struct {
        Page int `form:"page,default=1"`
        Size int `form:"size,default=20"`
    }
    
    func (p *PaginationReq) Sanitize() {
        if p.Page <= 0 { p.Page = 1 }
        if p.Size <= 0 { p.Size = 20 }
        if p.Size > 100 { p.Size = 100 } // Max page size limit
    }

    // types/some_list_request.go
    type GetSomeListRequest struct {
        types.PaginationReq
        // ... other filter params
    }

    // controller/some_controller.go
    var req types.GetSomeListRequest
    if !c.validator.CheckQuery(ctx, &req) {
        return
    }
    req.Sanitize() // Sanitize pagination params
    
    // ... call service with req.Page and req.Size
    ```

## 4、命名约定

- **文件名**: 使用下划线命名法（`user_ctl.go`, `user_svc.go`）。
- **包名**: 使用简短、小写、单数形式的名称（`controller`, `service`）。
- **结构体/接口**: 使用驼峰命名法（`UserService`, `UserCtl`）。

## 5、代码风格

- **格式化**: 使用 `gofmt` 格式化代码。
- **注释**: 为每个文件、函数、结构体添加必要的简短注释。
- **错误处理**: 使用 `pkg/response` 中定义的错误码，避免直接返回错误信息。
- **依赖管理**: 使用 `go mod` 管理依赖。
- **测试**: 为每个功能编写单元测试，并在 CI 中运行。
- **代码规范**: 遵循 Go 语言的编码规范，使用规范命名、使用 `go fmt` 格式化代码等；避免对某个模块的代码进行过度封装和过度设计，保证代码的可读性和可维护性。

## 6. 配置与日志

### 6.1 配置管理

- **配置分离**: 使用 `Viper` 等库管理配置。将配置与代码分离，并通过配置文件（如 `config.yaml`）或环境变量加载。
- **环境隔离**: 为开发 (`dev`)、测试 (`test`)、生产 (`prod`) 环境提供不同的配置文件。
- **禁止硬编码**: 严禁在代码中硬编码任何敏感信息（如数据库密码、API Key）。

### 6.2 日志记录

- **结构化日志**:`Zerolog` 等库进行结构化日志记录 (JSON 格式)。
- **日志级别**: 正确使用日志级别 (`Debug`, `Info`, `Warn`, `Error`, `Fatal`)。生产环境一般设置为 `Info` 级别。
- **包含上下文**: 日志中应包含 `request_id`, `user_id` 等上下文信息，便于追踪和调试。
并不是所有日志都记录上下文，导致日志文件过大，影响性能。
- **业务日志记录**: 根据业务程度进行适当记录，例如上报error或者debug记录进行调试；不要无限制的在业务中记录日志。例如api验证参数、参数验证等不需要记录日志，这属于开发阶段可解决的问题。

## 7. GORM 使用与高性能 SQL

### 7.1 GORM 规范

- **模型公共字段**: 如果模型存在id、created_at、updated_at 等字段，应增加 `BaseModel` ；如果存在软删除则加入`SoftDelete`。

- **避免 `SELECT *`**: 严禁使用 `db.First(&user)` 而不指定字段。必须使用 `db.Select("id", "name", ...).First(&user)` 来明确指定所需字段，减少不必要的数据传输和内存占用。
- **N+1 问题**: 警惕并解决 N+1 查询问题。使用 `Preload` 进行预加载。

  ```go
  // Bad: N+1 query
  db.Find(&users)
  for _, user := range users {
    db.Model(&user).Association("Profile").Find(&user.Profile)
  }

  // Good: Eager Loading
  db.Preload("Profile").Find(&users)
  ```

- **事务 (Transaction)**: 多个写操作必须放在一个事务中执行，使用 `db.Transaction` 来确保数据一致性。
- **错误检查**: GORM 的每个操作（Create, Query, Update, Delete）都必须检查返回的 `error`。特别是查询操作，需要判断 `gorm.ErrRecordNotFound`。

### 7.2 高性能 SQL 实践

- **索引 (Indexing)**:
  - **查询驱动**: 为 `WHERE` 子句中频繁用作查询条件的列建立索引。
  - **外键索引**: 所有外键列必须建立索引。
  - **组合索引**: 如果查询条件经常涉及多个列，应考虑建立组合索引。注意索引列的顺序。
  - **`EXPLAIN` 分析**: 对于复杂或慢查询，必须使用 `EXPLAIN` 分析其执行计划，找出性能瓶颈并进行优化。
- **避免大事务**: 事务应尽可能简短，只包含必要的操作，以减少锁定的时间和范围，提高并发性能。
- **批量操作**: 对于大量数据的插入或更新，使用 `CreateInBatches` 等批量操作方法。


## 8. 错误处理
### 8.1 错误处理

- **错误是值 (Errors are values)**: 函数如果可能出错，应将 `error` 作为最后一个返回值。
- **立即处理错误**: 不要使用 `_` 忽略错误。收到错误后应立即检查和处理。
- **错误包装 (Error Wrapping)**: 在调用链路中向上传递错误时，应添加上下文信息，方便调试。使用 `fmt.Errorf("...: %w", err)` 来包装错误。
- **特定错误判断**: 使用 `errors.Is()` 来判断是否为特定错误（例如 `gorm.ErrRecordNotFound`），使用 `errors.As()` 来转换到特定的错误类型。



## 9. 建表SQL规范
  - **数据库类型**: 符合Postgresql的数据类型，避免使用外键约束。
  - **表名**: 使用小写、复数形式的名称（`users`, `orders`）。
  - **字段名**: 使用小写、单数形式的名称（`user_id`, `order_id`）。
  - **字段类型**: 使用合适的数据类型（`varchar`, `int`, `timestamp`）,涉及到金额的字段使用int类型，单位为：分，更方便计算；根据不同字段职责使用不同长度，例如：用户名、昵称、手机号、邮箱等使用varchar(255)，密码使用varchar(255)，身份证号使用varchar(18)，地址使用varchar(255)，备注使用varchar(255)，其他字段根据实际情况设置。
  - **字段注释**: 为每个字段添加必要的注释，说明字段含义、约束等；在每个字段后面添加注释，例如：`user_id int comment '用户ID'`。
  - **特殊字段**: created_at、updated_at使用TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP；如果存在软删除，使用`is_del` smallint NOT NULL DEFAULT 0 COMMENT '是否删除 0否 1是'，并添加索引 `idx_is_del`。
  - **字段默认值**: 对于可为空的字段，应设置默认值（例如 `{}` 或 `''`）。
  - **字段注释**: 为每个字段添加必要的注释，说明字段含义、约束等；在每个字段后面添加注释，例如：`user_id int comment '用户ID'`。
  - **特殊字段**: created_at、updated_at使用TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP；如果存在软删除，使用`is_del` smallint NOT NULL DEFAULT 0 COMMENT '是否删除 0否 1是'，并添加索引 `idx_is_del`。
  - **字段默认值**: 对于可为空的字段，应设置默认值（例如 `{}` 或 `''`）。