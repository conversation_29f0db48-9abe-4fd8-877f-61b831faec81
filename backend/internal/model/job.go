package model

import (
	"time"

	"gorm.io/datatypes"
)

// Job represents a job posting.
type Job struct {
	ID                uint           `gorm:"primaryKey;type:bigserial" json:"id"`
	EnterpriseID      uint           `gorm:"not null;comment:企业ID" json:"enterprise_id"`
	UserID            uint           `gorm:"not null;comment:发布者ID" json:"user_id"`
	Title             string         `gorm:"size:255;not null;comment:职位名称" json:"title"`
	Description       string         `gorm:"type:text;comment:职位描述" json:"description"`
	Status            string         `gorm:"type:varchar(20);not null;default:'pending_review';comment:职位状态" json:"status"`
	SalaryMin         int            `gorm:"not null;default:0;comment:最低薪资(元)" json:"salary_min"`
	SalaryMax         int            `gorm:"not null;default:0;comment:最高薪资(元)" json:"salary_max"`
	ExperienceReq     int16          `gorm:"type:smallint;not null;default:0;comment:经验要求" json:"experience_req"`
	EducationReq      int16          `gorm:"type:smallint;not null;default:0;comment:学历要求" json:"education_req"`
	WorkType          int16          `gorm:"type:smallint;not null;default:1;comment:工作类型" json:"work_type"`
	WorkLocation      string         `gorm:"size:200;comment:工作地点" json:"work_location"`
	Latitude          float64        `gorm:"type:decimal(10,7);default:0;comment:纬度" json:"latitude"`
	Longitude         float64        `gorm:"type:decimal(10,7);default:0;comment:经度" json:"longitude"`
	RemoteWorkSupport bool           `gorm:"default:false;comment:是否支持远程工作" json:"remote_work_support"`
	Benefits          datatypes.JSON `gorm:"type:text[];comment:福利待遇" json:"benefits"`
	JobHighlights     datatypes.JSON `gorm:"type:text[];comment:职位亮点" json:"job_highlights"`
	Requirements      datatypes.JSON `gorm:"type:text[];comment:任职要求" json:"requirements"`
	ContactMethod     string         `gorm:"type:varchar(20);default:'phone';comment:联系方式" json:"contact_method"`
	IsUrgent          bool           `gorm:"not null;default:false;comment:是否紧急招聘" json:"is_urgent"`
	UrgentExpiresAt   *time.Time     `gorm:"comment:紧急招聘过期时间" json:"urgent_expires_at"`
	LastRefreshedAt   time.Time      `gorm:"default:CURRENT_TIMESTAMP;comment:最后刷新时间" json:"last_refreshed_at"`
	TodayRefreshCount int            `gorm:"not null;default:0;comment:今日刷新次数" json:"today_refresh_count"`
	ViewCount         int            `gorm:"not null;default:0;comment:浏览次数" json:"view_count"`
	ApplicationCount  int            `gorm:"not null;default:0;comment:申请人数" json:"application_count"`
	SearchContent     string         `gorm:"type:text;comment:搜索内容" json:"search_content"`
	CreatedAt         time.Time      `gorm:"type:timestamp(0);default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt         time.Time      `gorm:"type:timestamp(0);default:CURRENT_TIMESTAMP" json:"updated_at"`
	IsDel             int            `gorm:"not null;default:0;comment:软删除标志" json:"-"`

	// 关联字段
	Enterprise   *Enterprise      `gorm:"foreignKey:EnterpriseID" json:"enterprise,omitempty"`
	User         *User            `gorm:"foreignKey:UserID" json:"user,omitempty"`
	Applications []JobApplication `gorm:"foreignKey:JobID" json:"applications,omitempty"`

	// 虚拟字段
	HasApplied  bool    `gorm:"->:false;<-:false;comment:用户是否已申请" json:"has_applied"`
	IsFavorited bool    `gorm:"->:false;<-:false;comment:用户是否已收藏" json:"is_favorited"`
	Distance    float64 `gorm:"->:false;<-:false;comment:距离(km)" json:"distance,omitempty"`
}

// TableName returns the table name for the Job model.
func (Job) TableName() string {
	return "jobs"
}