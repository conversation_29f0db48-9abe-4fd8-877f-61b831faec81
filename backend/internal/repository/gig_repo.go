package repository

import (
	"context"
	"time"

	"bdb-backend/internal/model"
	"bdb-backend/internal/types"

	"gorm.io/gorm"
)

type GigRepository interface {
	Create(ctx context.Context, gig *model.Gig) error
	Find(ctx context.Context, id uint) (*model.Gig, error)
	Update(ctx context.Context, gig *model.Gig) error
	Delete(ctx context.Context, id uint) error
	GetList(ctx context.Context, req *types.GigListReq) ([]*model.Gig, int64, error)
	FindByUser(ctx context.Context, userID uint, status string) ([]model.Gig, error)
	IncrementApplyCount(ctx context.Context, id uint) error
	GetExpiredGigs(ctx context.Context) ([]model.Gig, error)

	// Calendar related methods
	FindByUserAndDateRange(ctx context.Context, userID uint, startTime, endTime time.Time) ([]*model.Gig, error)
	GetAppsByUserAndDateRange(ctx context.Context, userID uint, startTime, endTime time.Time) ([]*model.GigApplication, error)

	CreateApp(ctx context.Context, app *model.GigApplication) error
	FindApp(ctx context.Context, id uint) (*model.GigApplication, error)
	UpdateApp(ctx context.Context, app *model.GigApplication) error
	CheckUserApplied(ctx context.Context, gigID, userID uint) bool
	GetUserApps(ctx context.Context, userID uint, page, pageSize int) ([]*model.GigApplication, int64, error)
	GetApplications(ctx context.Context, gigID uint, page, pageSize int) ([]*model.GigApplication, int64, error)

	// DB 返回底层的 gorm.DB 实例
	DB() *gorm.DB
}

type gigRepository struct {
	db *gorm.DB
}

func NewGigRepository(db *gorm.DB) GigRepository {
	return &gigRepository{db: db}
}

// DB 返回底层的 gorm.DB 实例
func (r *gigRepository) DB() *gorm.DB {
	return r.db
}

func (r *gigRepository) Create(ctx context.Context, gig *model.Gig) error {
	return r.db.WithContext(ctx).Create(gig).Error
}

func (r *gigRepository) Find(ctx context.Context, id uint) (*model.Gig, error) {
	var gig model.Gig
	err := r.db.WithContext(ctx).
		Select("id", "user_id", "title", "description", "status", "salary", "people_count", "current_people_count",
			"start_time", "end_time", "work_duration", "expire_time", "approval_mode", "age_min", "age_max",
			"gender", "address", "detail_address", "full_address", "latitude", "longitude", "tags", "images",
			"created_at", "updated_at").
		First(&gig, id).Error
	return &gig, err
}

func (r *gigRepository) Update(ctx context.Context, gig *model.Gig) error {
	return r.db.WithContext(ctx).Save(gig).Error
}

func (r *gigRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&model.Gig{}, id).Error
}

func (r *gigRepository) GetList(ctx context.Context, req *types.GigListReq) ([]*model.Gig, int64, error) {
	var gigs []*model.Gig
	var total int64

	// Use separate query for count to avoid ORDER BY in count query
	countDB := r.db.WithContext(ctx).Model(&model.Gig{}).
		Where("status = ?", "recruiting")

	if err := countDB.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Main query with proper optimization
	err := r.db.WithContext(ctx).
		Select("id", "user_id", "title", "description", "status", "salary", "people_count", "current_people_count",
			"start_time", "end_time", "address", "latitude", "longitude", "tags", "created_at", "updated_at").
		Where("status = ?", "recruiting").
		Scopes(model.Paginate(req.Page, req.PageSize)).
		Order("created_at DESC").
		Find(&gigs).Error
	return gigs, total, err
}

func (r *gigRepository) FindByUser(ctx context.Context, userID uint, status string) ([]model.Gig, error) {
	var gigs []model.Gig
	query := r.db.WithContext(ctx).
		Select("id", "user_id", "title", "description", "status", "salary", "people_count", "current_people_count",
			"start_time", "end_time", "address", "created_at", "updated_at").
		Where("user_id = ?", userID)

	if status != "" {
		query = query.Where("status = ?", status)
	}

	err := query.Order("created_at DESC").Find(&gigs).Error
	return gigs, err
}

func (r *gigRepository) IncrementApplyCount(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).
		Model(&model.Gig{}).
		Where("id = ?", id).
		Update("current_people_count", gorm.Expr("current_people_count + 1")).Error
}

func (r *gigRepository) GetExpiredGigs(ctx context.Context) ([]model.Gig, error) {
	var gigs []model.Gig
	err := r.db.WithContext(ctx).
		Select("id", "user_id", "title", "status", "expire_time").
		Where("expire_time < ? AND status IN (?, ?)",
			time.Now(), "recruiting", "in_progress").
		Order("expire_time ASC").
		Find(&gigs).Error
	return gigs, err
}

func (r *gigRepository) CreateApp(ctx context.Context, app *model.GigApplication) error {
	return r.db.WithContext(ctx).Create(app).Error
}

func (r *gigRepository) FindApp(ctx context.Context, id uint) (*model.GigApplication, error) {
	var app model.GigApplication
	err := r.db.WithContext(ctx).
		Select("id", "gig_id", "user_id", "status", "message", "applicant_phone", "applicant_name", "reviewer_note", "reviewed_at", "created_at", "updated_at").
		Preload("GigInfo", func(db *gorm.DB) *gorm.DB {
			return db.Select("id", "title", "description", "salary", "start_time", "end_time", "address")
		}).
		Preload("ApplicantInfo", func(db *gorm.DB) *gorm.DB {
			return db.Select("id", "nickname", "avatar", "phone")
		}).
		First(&app, id).Error
	return &app, err
}

func (r *gigRepository) UpdateApp(ctx context.Context, app *model.GigApplication) error {
	return r.db.WithContext(ctx).Save(app).Error
}

func (r *gigRepository) CheckUserApplied(ctx context.Context, gigID, userID uint) bool {
	var exists bool
	r.db.WithContext(ctx).
		Model(&model.GigApplication{}).
		Select("1").
		Where("gig_id = ? AND user_id = ?", gigID, userID).
		Limit(1).
		Scan(&exists)
	return exists
}

func (r *gigRepository) GetUserApps(ctx context.Context, userID uint, page, pageSize int) ([]*model.GigApplication, int64, error) {
	var apps []*model.GigApplication
	var total int64

	// Separate count query
	countDB := r.db.WithContext(ctx).Model(&model.GigApplication{}).
		Where("user_id = ?", userID)

	if err := countDB.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Main query with optimized preloading
	err := r.db.WithContext(ctx).
		Select("id", "gig_id", "user_id", "status", "message", "applicant_phone", "applicant_name",
			"reviewer_note", "reviewed_at", "created_at", "updated_at").
		Where("user_id = ?", userID).
		Preload("GigInfo", func(db *gorm.DB) *gorm.DB {
			return db.Select("id", "title", "description", "salary", "start_time", "end_time", "address")
		}).
		Scopes(model.Paginate(page, pageSize)).
		Order("created_at DESC").
		Find(&apps).Error
	return apps, total, err
}

func (r *gigRepository) GetApplications(ctx context.Context, gigID uint, page, pageSize int) ([]*model.GigApplication, int64, error) {
	var apps []*model.GigApplication
	var total int64

	// Separate count query
	countDB := r.db.WithContext(ctx).Model(&model.GigApplication{}).
		Where("gig_id = ?", gigID)

	if err := countDB.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Main query with optimized preloading
	err := r.db.WithContext(ctx).
		Select("id", "gig_id", "user_id", "status", "message", "applicant_phone", "applicant_name",
			"reviewer_note", "reviewed_at", "created_at", "updated_at").
		Where("gig_id = ?", gigID).
		Preload("ApplicantInfo", func(db *gorm.DB) *gorm.DB {
			return db.Select("id", "nickname", "avatar", "phone")
		}).
		Scopes(model.Paginate(page, pageSize)).
		Order("created_at DESC").
		Find(&apps).Error
	return apps, total, err
}

// FindByUserAndDateRange 根据用户ID和日期范围获取零工列表
func (r *gigRepository) FindByUserAndDateRange(ctx context.Context, userID uint, startTime, endTime time.Time) ([]*model.Gig, error) {
	var gigs []*model.Gig
	err := r.db.WithContext(ctx).
		Select("id", "user_id", "title", "description", "status", "salary", "start_time", "end_time", "address").
		Where("user_id = ? AND start_time >= ? AND start_time <= ?", userID, startTime, endTime).
		Order("start_time ASC").
		Find(&gigs).Error
	return gigs, err
}

// GetAppsByUserAndDateRange 根据用户ID和日期范围获取申请列表
func (r *gigRepository) GetAppsByUserAndDateRange(ctx context.Context, userID uint, startTime, endTime time.Time) ([]*model.GigApplication, error) {
	var apps []*model.GigApplication
	err := r.db.WithContext(ctx).
		Table("gig_applications").
		Select("gig_applications.id", "gig_applications.gig_id", "gig_applications.user_id",
			"gig_applications.status", "gig_applications.created_at", "gig_applications.updated_at").
		Joins("INNER JOIN gigs ON gig_applications.gig_id = gigs.id").
		Where("gig_applications.user_id = ?", userID).
		Where("gigs.start_time >= ? AND gigs.start_time <= ?", startTime, endTime).
		Order("gig_applications.created_at ASC").
		Find(&apps).Error
	return apps, err
}
