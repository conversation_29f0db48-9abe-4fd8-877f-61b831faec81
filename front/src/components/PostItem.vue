<template>
  <view
    class="post-item bg-white mx-20rpx my-20rpx rounded-lg p-30rpx"
    @tap="onItemClick"
  >
    <!-- 用户信息 -->
    <view class="user-info flex items-center mb-20rpx">
      <image-loader :src="post.avatar" class="user-avatar"></image-loader>
      <view class="ml-20rpx flex-1">
        <view class="text-30rpx font-bold">{{ post.username }}</view>
        <view class="text-24rpx color-grey">
          {{ post.position }} · {{ post.company }}
        </view>
      </view>
      <view class="follow-btn" v-if="!post.isFollowing" @tap.stop="onFollow">
        <text class="i-carbon-add text-18rpx mr-5rpx"></text>
        <text class="text-26rpx">关注</text>
      </view>
      <view class="following-btn" v-else @tap.stop="onUnfollow">
        <text class="i-carbon-checkmark text-24rpx mr-5rpx"></text>
        <text class="text-26rpx">已关注</text>
      </view>
    </view>

    <!-- 动态内容 -->
    <view class="post-content mb-20rpx">
      <view class="content-text color-main line-height-44rpx mb-20rpx">
        {{ post.content }}
      </view>

      <!-- 图片展示 -->
      <image-grid :images="post.images" v-if="post.images && post.images.length > 0" />

      <!-- 视频展示 -->
      <view class="post-video-container" v-if="post.video">
        <video
          :src="post.video"
          class="post-video"
          :poster="post.videoCover"
          object-fit="cover"
          @error="onVideoError"
        ></video>
      </view>

      <!-- 话题和位置 -->
      <view class="meta-info mt-20rpx">
        <view v-if="post.topic" class="topic-tag">
          {{ post.topic }}
        </view>
        <view v-if="post.location" class="location-tag flex items-center mt-10rpx">
           <text class="i-carbon-location color-grey text-28rpx"></text>
           <text class="ml-10rpx text-26rpx color-grey">{{ post.location }}</text>
        </view>
      </view>
    </view>

    <!-- 互动区域 -->
    <view class="post-actions flex justify-between items-center border-top pt-20rpx">
      <!-- 左侧举报 -->
       <view class="action-item flex items-center" @tap.stop="onReport">
        <text class="i-carbon-warning-hexagon color-grey text-40rpx"></text>
      </view>

      <!-- 右侧点赞、评论、分享 -->
      <view class="flex items-center">
        <view class="action-item flex items-center ml-40rpx" @tap.stop="onLike">
          <text
            :class="
              post.isLiked
                ? 'i-solar:like-bold text-red'
                : 'i-solar:like-broken color-grey'
            "
            class="text-36rpx"
          ></text>
          <text
            class="ml-10rpx text-26rpx"
            :class="post.isLiked ? 'text-primary' : 'color-grey'"
          >
            {{ post.likes }}
          </text>
        </view>
        <view class="action-item flex items-center ml-40rpx" @tap.stop="onComment">
          <text class="i-solar:chat-round-call-linear color-grey text-36rpx"></text>
          <text class="ml-10rpx text-26rpx color-grey">{{ post.comments }}</text>
        </view>
        <view class="action-item flex items-center ml-40rpx" @tap.stop="onShare">
          <text class="i-solar:share-outline color-grey text-36rpx"></text>
          <text class="ml-10rpx text-26rpx color-grey">分享</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import ImageGrid from './post/ImageGrid.vue';

defineProps({
  post: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits([
  "follow",
  "unfollow",
  "like",
  "comment",
  "share",
  "click",
]);

// 点击整个帖子
const onItemClick = () => {
  emit("click");
};

// 点赞
const onLike = () => {
  emit("like");
};

// 评论
const onComment = () => {
  emit("comment");
};

// 分享
const onShare = () => {
  emit("share");
};

// 关注
const onFollow = () => {
  emit("follow");
};

// 取消关注
const onUnfollow = () => {
  emit("unfollow");
};

// 举报
const onReport = () => {
  uni.showToast({
    title: "举报功能开发中",
    icon: "none",
  });
};

// 视频加载错误
const onVideoError = () => {
  uni.showToast({
    title: "视频加载失败",
    icon: "none",
  });
};
</script>

<style lang="scss" scoped>
.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
}

.follow-btn {
  padding: 6rpx 20rpx;
  background-color: rgba(255, 109, 0, 0.1);
  color: $primary;
  border-radius: 30rpx;
  border: 1rpx solid $primary;
  display: flex;
  align-items: center;
}

.following-btn {
  padding: 6rpx 20rpx;
  color: #999;
  border-radius: 30rpx;
  border: 1rpx solid #ddd;
  display: flex;
  align-items: center;
}

.content-text {
  font-size: 30rpx;
}

.meta-info {
  .topic-tag {
    display: inline-block;
    padding: 4rpx 16rpx;
    background-color: #eef3ff;
    color: #409eff;
    font-size: 24rpx;
    border-radius: 20rpx;
  }
}

.post-video-container {
  width: 100%;
  height: 400rpx;
  border-radius: 8rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
}

.post-video {
  width: 100%;
  height: 100%;
}

.border-top {
  border-top: 1rpx solid #f0f0f0;
}

.pt-20rpx {
  padding-top: 20rpx;
}

.line-height-44rpx {
  line-height: 44rpx;
}

.text-primary {
  color: $primary;
}

.text-red {
  color: #ff5555;
}
</style>
