<template>
  <view class="smart-recommend-container">
    <!-- 智能推荐标题 -->
    <view class="recommend-title flex items-center px-20rpx py-20rpx">
      <text class="i-carbon-ai text-primary text-32rpx mr-10rpx"></text>
      <text class="text-30rpx font-bold">为你推荐</text>
      <text class="text-24rpx text-grey ml-10rpx">基于你的浏览偏好</text>
    </view>

    <!-- 动态推荐模块 -->
    <view
      v-for="(module, index) in recommendModules"
      :key="index"
      class="mb-20rpx"
    >
      <!-- 招聘推荐 -->
      <JobContent v-if="module.type === 'job'" />

      <!-- 房产推荐 -->
      <HouseContent v-if="module.type === 'house'" />

      <!-- 零工推荐 -->
      <GigContent v-if="module.type === 'gig'" />

      <!-- 交友推荐 -->
      <DatingContent v-if="module.type === 'dating'" />
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed, ref, onMounted } from "vue";
import JobContent from "./JobContent.vue";
import HouseContent from "./HouseContent.vue";
import GigContent from "./GigContent.vue";
import DatingContent from "./DatingContent.vue";

// 模拟用户历史浏览记录 - 根据不同用户类型生成不同的浏览偏好
const generateUserBrowseHistory = () => {
  // 模拟不同类型用户的浏览习惯
  const userTypes = [
    { type: "job_seeker", job: 25, house: 5, gig: 8, dating: 2 },
    { type: "house_hunter", job: 3, house: 20, gig: 2, dating: 1 },
    { type: "freelancer", job: 8, house: 3, gig: 22, dating: 5 },
    { type: "young_professional", job: 12, house: 10, gig: 6, dating: 15 },
    { type: "balanced", job: 10, house: 8, gig: 12, dating: 6 },
  ];

  // 随机选择一种用户类型
  const randomUserType =
    userTypes[Math.floor(Math.random() * userTypes.length)];

  // 添加随机波动，使数据更真实
  const addRandomVariation = (base: number) => {
    const variation = Math.floor(Math.random() * 6) - 3; // -3到+3的随机变化
    return Math.max(0, base + variation);
  };

  return {
    job: addRandomVariation(randomUserType.job),
    house: addRandomVariation(randomUserType.house),
    gig: addRandomVariation(randomUserType.gig),
    dating: addRandomVariation(randomUserType.dating),
  };
};

const userBrowseHistory = ref(generateUserBrowseHistory());

// 浏览统计数据
const browseStats = computed(() => [
  {
    label: "招聘",
    count: userBrowseHistory.value.job,
    icon: "i-solar-user-id-outline",
    color: "#ff6d00",
  },
  {
    label: "房产",
    count: userBrowseHistory.value.house,
    icon: "i-solar-home-2-outline",
    color: "#00bcd4",
  },
  {
    label: "零工",
    count: userBrowseHistory.value.gig,
    icon: "i-solar-user-hands-outline",
    color: "#4caf50",
  },
  {
    label: "交友",
    count: userBrowseHistory.value.dating,
    icon: "i-solar-heart-outline",
    color: "#e91e63",
  },
]);

// 根据浏览次数动态生成推荐模块
const recommendModules = computed(() => {
  const modules = [];
  const history = userBrowseHistory.value;

  // 创建包含浏览次数的数组
  const moduleTypes = [
    { type: "job", count: history.job, priority: 1 },
    { type: "house", count: history.house, priority: 2 },
    { type: "gig", count: history.gig, priority: 3 },
    { type: "dating", count: history.dating, priority: 4 },
  ];

  // 按浏览次数排序（降序）
  moduleTypes.sort((a, b) => {
    if (b.count !== a.count) {
      return b.count - a.count; // 浏览次数多的优先
    }
    return a.priority - b.priority; // 浏览次数相同时按优先级
  });

  // 只显示浏览次数大于0的模块，最多显示3个
  moduleTypes.forEach((module, index) => {
    if (module.count > 0 && index < 3) {
      modules.push({
        type: module.type,
        count: module.count,
      });
    }
  });

  // 如果没有浏览记录，默认显示招聘
  if (modules.length === 0) {
    modules.push({ type: "job", count: 0 });
  }

  return modules;
});

// 模拟增加浏览记录（实际项目中这些数据应该从后端获取）
const simulateUserActivity = () => {
  // 模拟用户在使用过程中增加浏览记录
  setInterval(() => {
    const types = ["job", "house", "gig", "dating"];
    const randomType = types[
      Math.floor(Math.random() * types.length)
    ] as keyof typeof userBrowseHistory.value;
    userBrowseHistory.value[randomType]++;
  }, 30000); // 每30秒模拟一次浏览
};

// 组件挂载时开始模拟
onMounted(() => {
  simulateUserActivity();
});
</script>

<style scoped>
.smart-recommend-container {
  padding: 0;
}

.recommend-title {
  background: linear-gradient(
    135deg,
    rgba(255, 109, 0, 0.1),
    rgba(255, 149, 0, 0.05)
  );
  margin: 0 16rpx;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
}
</style>
