<template>
  <view class="house-card" @tap="navigateToDetail">
    <!-- 左侧图片区域 -->
    <view class="image-container">
      <!-- 特殊标签：看房有礼、新房等 -->
      <view v-if="house.specialTag" class="special-tag">{{
        house.specialTag
      }}</view>
      <!-- VR看房标签 -->
      <view v-if="house.hasVR" class="vr-tag flex items-center">
        <text class="i-carbon-view text-16rpx mr-4rpx"></text>
        <text>VR看房</text>
      </view>
      <image
        :src="house.image"
        mode="aspectFill"
        class="house-image"
        :lazy-load="true"
      ></image>
    </view>

    <!-- 右侧信息区域 -->
    <view class="info-container">
      <!-- 标题行 -->
      <view class="title-row">
        <!-- 房源类型标签 -->
        <view v-if="house.type" class="tag tag-primary tag-sm">{{
          house.type
        }}</view>
        <text class="house-title">{{ formatTitle() }}</text>
      </view>

      <!-- 信息内容区 -->
      <view class="info-content">
        <!-- 房源描述信息 -->
        <view class="house-description">
          <!-- 第二行信息 - 根据房源类型显示不同内容 -->
          <view class="description-line">
            <text class="description-text">{{ formatSecondLine() }}</text>
          </view>

          <!-- 租房特殊信息 -->
          <view v-if="type === 'rent'" class="rent-features">
            <text>{{ formatThirdLine() }}</text>
          </view>

          <!-- 标签区域 -->
          <view
            class="tag-container"
            v-if="house.tags && house.tags.length > 0"
          >
            <text
              v-for="(tag, index) in house.tags.slice(0, 3)"
              :key="index"
              class="tag tag-info tag-sm"
              >{{ tag }}</text
            >
          </view>
        </view>
      </view>

      <!-- 价格信息行 -->
      <view class="price-row" :class="{ 'rent-price': type === 'rent' }">
        <view class="price-main">
          <text class="price-value">{{ formatPrice() }}</text>
          <text class="price-unit">{{ formatPriceUnit() }}</text>
        </view>
        <view class="price-extra">
          <text v-if="type === 'second' || type === 'new'" class="unit-price">{{
            house.unitPrice || ""
          }}</text>
          <text
            v-if="type !== 'rent' && house.location"
            class="location-text"
            >{{ house.location }}</text
          >
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from "vue";

const props = defineProps({
  house: {
    type: Object,
    required: true,
  },
  // 房源类型：rent(租房)、second(二手房)、new(新房)、commercial(商铺办公)
  type: {
    type: String,
    default: "second",
  },
});

// 格式化标题
const formatTitle = () => {
  if (!props.house.title) return "";

  switch (props.type) {
    case "rent":
      // 租房格式: "整租3居 · [Community Name]"
      if (props.house.layout && props.house.community) {
        const rentType = props.house.rentType || "整租";
        const rooms = props.house.layout.match(/(\d+)室/)
          ? props.house.layout.match(/(\d+)室/)[1]
          : "";
        return `${rentType}${rooms}居 · ${props.house.community}`;
      }
      return props.house.title;

    case "second":
    case "new":
      // 二手房/新房格式: "[Community Name] [Room Layout]"
      if (props.house.community && props.house.layout) {
        return `${props.house.community} ${props.house.layout}`;
      }
      return props.house.title;

    case "commercial":
      // 商业地产保持原标题
      return props.house.title;

    default:
      return props.house.title;
  }
};

// 格式化第二行信息
const formatSecondLine = () => {
  switch (props.type) {
    case "rent":
      // 租房: 面积、楼层、朝向、户型
      const parts = [];
      if (props.house.area) parts.push(`${props.house.area}平米`);
      if (props.house.floor) parts.push(props.house.floor);
      if (props.house.direction) parts.push(props.house.direction);
      if (props.house.layout) parts.push(props.house.layout);
      return parts.join(" | ");

    case "second":
    case "new":
      // 二手房/新房: 面积和朝向
      const parts2 = [];
      if (props.house.area) parts2.push(`${props.house.area}平米`);
      if (props.house.direction) parts2.push(props.house.direction);
      if (props.house.floor) parts2.push(props.house.floor);
      return parts2.join(" | ");

    case "commercial":
      // 商业地产: 面积和楼层
      const commercialParts = [];
      if (props.house.area) commercialParts.push(`${props.house.area}平米`);
      if (props.house.floor) commercialParts.push(props.house.floor);
      return commercialParts.join(" | ");

    default:
      return "";
  }
};

// 格式化第三行信息
const formatThirdLine = () => {
  switch (props.type) {
    case "rent":
      // 租房: 特色标签如"押一付一"、"民水民电"等
      const rentFeatures = [];
      if (props.house.paymentMethod)
        rentFeatures.push(props.house.paymentMethod);
      if (props.house.utilities) rentFeatures.push(props.house.utilities);
      if (props.house.extraInfo) rentFeatures.push(props.house.extraInfo);
      return rentFeatures.join(" · ") || "";

    case "second":
    case "new":
    case "commercial":
      return "";

    default:
      return "";
  }
};

// 格式化价格
const formatPrice = () => {
  if (!props.house.price) return "";

  if (typeof props.house.price === "string") {
    return props.house.price.replace(/万|元\/月|元\/平|元/, "");
  }

  return props.house.price.toString();
};

// 格式化价格单位
const formatPriceUnit = () => {
  if (!props.house.price) return "";

  switch (props.type) {
    case "rent":
      return "元/月";
    case "second":
      return "万";
    case "new":
      return "万/套";
    case "commercial":
      return props.house.priceType === "sale" ? "万" : "元/月";
    default:
      return "";
  }
};

// 跳转到详情页
const navigateToDetail = () => {
  uni.navigateTo({
    url: `/pages/house/${props.type}/detail?id=${props.house.id}`,
  });
};
</script>

<style lang="scss" scoped>
.house-card {
  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: $bg-card;
  border-radius: $radius;
  margin-bottom: $spacing-10;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.image-container {
  position: relative;
  width: 240rpx;
  height: 230rpx;
  flex-shrink: 0;
}

.house-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.special-tag {
  position: absolute;
  top: 0;
  left: 0;
  background-color: $text-red;
  color: $text-inverse;
  font-size: $font-size-xs;
  padding: 2rpx 10rpx;
  border-top-left-radius: $radius;
  border-bottom-right-radius: $radius-sm;
  z-index: 1;
}

.vr-tag {
  position: absolute;
  bottom: 10rpx;
  left: 10rpx;
  background-color: rgba(0, 0, 0, 0.6);
  color: $text-inverse;
  font-size: $font-size-xs;
  padding: 2rpx 10rpx;
  border-radius: $radius-sm;
  z-index: 1;
}

.info-container {
  flex: 1;
  padding: $spacing-6 $spacing-8;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.title-row {
  display: flex;
  align-items: flex-start;
  align-items: center;
  margin-bottom: $spacing-4;
}

.house-title {
  font-size: $font-size-lg;
  font-weight: 500;
  color: $text-base;
  line-height: 1.4;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.info-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.house-description {
  flex: 1;
}

.description-line {
  margin-bottom: 4rpx;
}

.description-text {
  font-size: $font-size-xs;
  color: $text-secondary;
  line-height: 1.3;
}

.rent-features {
  font-size: $font-size-xs;
  color: $text-grey;
  margin-bottom: 4rpx;
}

.price-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-top: $spacing-4;
}

.price-row.rent-price {
  margin-top: $spacing-6;
}

.price-main {
  display: flex;
  align-items: baseline;
}

.price-value {
  font-size: $font-size-lg;
  font-weight: 600;
  color: $text-red;
}

.price-unit {
  font-size: $font-size-xs;
  color: $text-red;
  margin-left: 2rpx;
}

.price-extra {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.unit-price {
  font-size: $font-size-xs;
  color: $text-grey;
  margin-bottom: 2rpx;
}

.location-text {
  font-size: $font-size-xs;
  color: $text-grey;
  max-width: 200rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
