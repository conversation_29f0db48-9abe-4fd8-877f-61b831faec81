<template>
  <view class="job-item card" @tap="handleTap">
    <!-- 职位头部信息，包含职位名称和薪资 -->
    <view class="job-header">
      <view class="job-title-wrapper">
        <text class="job-title line-clamp-2">{{ job.title }}</text>
        <view v-if="job.isUrgent" class="urgent-tag ml-10rpx">急聘</view>
      </view>
      <view class="salary text-primary">{{ job.salary }}</view>
    </view>

    <!-- 公司信息 -->
    <view class="company-info flex items-center mt-12rpx">
      <text class="company-name text-26rpx">{{ job.companyName }}</text>
      <text class="text-26rpx mx-12rpx">|</text>
      <text class="text-26rpx">{{ job.industry }}</text>
    </view>

    <!-- 职位标签 -->
    <view class="tags flex flex-wrap mt-12rpx">
      <view
        v-for="(tag, tagIndex) in job.tags.slice(0, 3)"
        :key="tagIndex"
        class="tag tag-sm"
      >
        {{ tag }}
      </view>
    </view>

    <!-- 底部位置和时间信息 -->
    <view class="divider my-20rpx"></view>

    <view class="flex justify-between items-center">
      <view class="flex items-center">
        <text class="i-carbon-location text-grey text-24rpx mr-6rpx"></text>
        <text class="area text-grey text-26rpx">{{ job.area }}</text>
      </view>
      <text class="text-grey text-24rpx">{{ job.publishTime }}</text>
    </view>
  </view>
</template>

<script setup lang="ts">
const props = defineProps({
  job: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits(["click"]);

const handleTap = () => {
  emit("click", props.job);
};
</script>

<style lang="scss" scoped>
.job-item {
  .job-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
  }

  .job-title-wrapper {
    display: flex;
    align-items: center;
    flex: 1;
    flex-wrap: wrap;
  }

  .job-title {
    font-size: 34rpx;
    font-weight: 500;
    color: var(--text-base);
    line-height: 1.4;
    max-width: 60%;
  }

  .salary {
    color: var(--primary);
    flex-shrink: 0;
    font-size: 30rpx;
    font-weight: 500;
    text-align: right;
    max-width: 40%;
  }

  .company-info {
    color: var(--text-info);
  }

  .divider {
    height: 1rpx;
    background-color: var(--border-color);
  }
}
</style>
