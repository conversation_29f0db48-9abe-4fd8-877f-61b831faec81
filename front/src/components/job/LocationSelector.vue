<template>
  <view class="location-selector">
    <view class="location-content">
      <view class="px-30rpx py-20rpx mb-20rpx">
        <button class="location-btn-small" @tap="getLocation">
          <text class="i-carbon-location mr-8rpx"></text>
          <text>获取当前位置</text>
        </button>
      </view>

      <view class="flex-col location-view">
        <!-- 地址选择标签页 -->
        <view class="flex location-tabs">
          <view
            class="location-tab"
            :class="{ 'location-tab-active': addressTabIndex === 0 }"
            @tap="switchAddressTab(0)"
          >
            <text>{{ selectedProvinceName || "选择省份" }}</text>
          </view>
          <text class="mx-10rpx color-grey">></text>
          <view
            class="location-tab"
            :class="{ 'location-tab-active': addressTabIndex === 1 }"
            @tap="switchAddressTab(1)"
          >
            <text>{{ selectedCityName || "选择城市" }}</text>
          </view>
          <text class="mx-10rpx color-grey">></text>
          <view
            class="location-tab"
            :class="{ 'location-tab-active': addressTabIndex === 2 }"
            @tap="switchAddressTab(2)"
          >
            <text>{{ selectedDistrictName || "选择区县" }}</text>
          </view>
        </view>

        <!-- 省市区列表 -->
        <scroll-view scroll-y class="location-scroll">
          <!-- 热门城市 - 仅在省份列表时显示 -->
          <block v-if="addressTabIndex === 0">
            <view class="hot-cities-section">
              <view class="hot-cities-title">热门城市</view>
              <view class="hot-cities-list">
                <view
                  v-for="(city, index) in hotCities"
                  :key="index"
                  class="hot-city-item"
                  @tap="selectHotCity(city)"
                >
                  {{ city.name }}
                </view>
              </view>
            </view>
            <view class="location-divider"></view>
            <view class="province-list-title">省份列表</view>

            <view
              v-for="(province, index) in locationData"
              :key="index"
              class="location-list-item"
              :class="{
                'location-selected-item': selectedProvince === index,
              }"
              @tap="selectProvince(index)"
            >
              {{ province.name }}
            </view>
          </block>

          <!-- 城市列表 -->
          <block v-if="addressTabIndex === 1">
            <view
              v-for="(city, index) in currentCities"
              :key="index"
              class="location-list-item"
              :class="{ 'location-selected-item': selectedCity === index }"
              @tap="selectCity(index)"
            >
              {{ city.name }}
            </view>
          </block>

          <!-- 区县列表 -->
          <block v-if="addressTabIndex === 2">
            <view
              v-for="(district, index) in currentDistricts"
              :key="index"
              class="location-list-item"
              :class="{
                'location-selected-item': selectedDistrict === index,
              }"
              @tap="selectDistrict(index)"
            >
              {{ district.name }}
            </view>
          </block>
        </scroll-view>
      </view>

      <!-- 详细地址输入 -->
      <view class="location-detail-section">
        <view class="location-detail-header">详细地址</view>
        <view class="location-detail-input-wrap">
          <input
            type="text"
            v-model="localLocationDetail"
            placeholder="请输入详细地址（如街道、小区、写字楼、门牌号等）"
            class="location-detail-input"
            @focus="handleAddressInputFocus"
            adjust-position
          />
        </view>
        <view class="location-detail-tips"
          >详细地址可以帮助求职者更准确地了解工作地点</view
        >

        <view class="location-action-btns flex">
          <view class="location-map-btn" @tap="openLocationChooser()">
            <text class="i-carbon-map mr-8rpx"></text>
            <text>地图选点</text>
          </view>
          <view class="location-btn flex-1" @tap="confirmLocation">确定</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, defineProps, defineEmits, watch } from "vue";

// 定义类型
interface Province {
  name: string;
  cities: City[];
}

interface City {
  name: string;
  districts: District[];
}

interface District {
  name: string;
}

interface HotCity {
  province: number;
  city: number;
  name: string;
}

const props = defineProps({
  locationData: {
    type: Array as () => Province[],
    required: true,
  },
  hotCities: {
    type: Array as () => HotCity[],
    required: true,
  },
  selectedProvince: {
    type: Number,
    default: 0,
  },
  selectedCity: {
    type: Number,
    default: 0,
  },
  selectedDistrict: {
    type: Number,
    default: -1,
  },
  locationDetail: {
    type: String,
    default: "",
  },
});

const emit = defineEmits([
  "update:selectedProvince",
  "update:selectedCity",
  "update:selectedDistrict",
  "update:locationDetail",
  "confirm",
  "map-select",
]);

// 地址选择Tab
const addressTabIndex = ref(0);

// 本地数据，用于双向绑定
const localSelectedProvince = ref(props.selectedProvince);
const localSelectedCity = ref(props.selectedCity);
const localSelectedDistrict = ref(props.selectedDistrict);
const localLocationDetail = ref(props.locationDetail);

// 监听props变化，同步到本地数据
watch(
  () => props.selectedProvince,
  (val) => {
    localSelectedProvince.value = val;
  }
);
watch(
  () => props.selectedCity,
  (val) => {
    localSelectedCity.value = val;
  }
);
watch(
  () => props.selectedDistrict,
  (val) => {
    localSelectedDistrict.value = val;
  }
);
watch(
  () => props.locationDetail,
  (val) => {
    localLocationDetail.value = val;
  }
);

// 计算属性：已选择的省市区名称
const selectedProvinceName = computed(() => {
  if (
    localSelectedProvince.value >= 0 &&
    props.locationData[localSelectedProvince.value]
  ) {
    return props.locationData[localSelectedProvince.value].name;
  }
  return "";
});

const currentCities = computed(() => {
  return props.locationData[localSelectedProvince.value]?.cities || [];
});

const selectedCityName = computed(() => {
  if (
    localSelectedCity.value >= 0 &&
    currentCities.value[localSelectedCity.value]
  ) {
    return currentCities.value[localSelectedCity.value].name;
  }
  return "";
});

const currentDistricts = computed(() => {
  return currentCities.value[localSelectedCity.value]?.districts || [];
});

const selectedDistrictName = computed(() => {
  if (
    localSelectedDistrict.value >= 0 &&
    currentDistricts.value[localSelectedDistrict.value]
  ) {
    return currentDistricts.value[localSelectedDistrict.value].name;
  }
  return "";
});

// 切换地址选择器标签页
const switchAddressTab = (index: number) => {
  addressTabIndex.value = index;
};

// 选择省份
const selectProvince = (index: number) => {
  localSelectedProvince.value = index;
  emit("update:selectedProvince", index);
  localSelectedCity.value = 0;
  emit("update:selectedCity", 0);
  localSelectedDistrict.value = -1;
  emit("update:selectedDistrict", -1);
  // 自动切换到城市选择
  switchAddressTab(1);
};

// 选择城市
const selectCity = (index: number) => {
  localSelectedCity.value = index;
  emit("update:selectedCity", index);
  localSelectedDistrict.value = -1;
  emit("update:selectedDistrict", -1);
  // 自动切换到区县选择
  switchAddressTab(2);
};

// 选择区县
const selectDistrict = (index: number) => {
  localSelectedDistrict.value = index;
  emit("update:selectedDistrict", index);
};

// 处理地址输入框聚焦
const handleAddressInputFocus = () => {
  // 确保详细地址输入区域可见
  setTimeout(() => {
    uni.pageScrollTo({
      scrollTop: 1000,
      duration: 300,
    });
  }, 300);
};

// 选择热门城市
const selectHotCity = (city: HotCity) => {
  selectProvince(city.province);
  setTimeout(() => {
    selectCity(city.city);
  }, 100);
};

// 获取用户当前位置
const getLocation = () => {
  uni.showLoading({
    title: "获取位置中...",
  });

  uni.getLocation({
    type: "gcj02",
    success: (res) => {
      uni.hideLoading();
      // 直接打开地图选择页面
      openLocationChooser(res.latitude, res.longitude);
    },
    fail: () => {
      uni.hideLoading();
      uni.showToast({
        title: "获取位置失败",
        icon: "none",
      });
    },
  });
};

// 打开地图选择位置
const openLocationChooser = (latitude = 0, longitude = 0) => {
  emit("map-select", { latitude, longitude });
};

// 确认地址
const confirmLocation = () => {
  if (localSelectedDistrict.value >= 0) {
    emit("update:locationDetail", localLocationDetail.value.trim());
    emit("confirm");
  } else {
    uni.showToast({
      title: "请选择区县",
      icon: "none",
    });
  }
};

// 监听locationDetail变化并同步
watch(localLocationDetail, (val) => {
  emit("update:locationDetail", val);
});
</script>

<style lang="scss" scoped>
.location-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
}

.location-btn-small {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 12rpx 24rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  font-size: 26rpx;
  border: none;
  color: #666;
}

.location-view {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: calc(100% - 300rpx);
  background-color: #f8f8f8;
}

.location-tabs {
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background-color: #ffffff;
}

.location-tab {
  padding: 12rpx 24rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  background-color: #f5f5f5;
  color: #666;
  margin-right: 10rpx;
}

.location-tab-active {
  background-color: rgba(255, 109, 0, 0.1);
  color: #ff6d00;
  font-weight: bold;
}

.location-scroll {
  flex: 1;
  height: 100%;
  background-color: #ffffff;
}

.location-list-item {
  padding: 25rpx 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
  font-size: 28rpx;
  color: #333;
}

.location-selected-item {
  color: #ff6d00;
  font-weight: bold;
  position: relative;
}

.location-selected-item:after {
  content: "";
  position: absolute;
  right: 30rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 40rpx;
  height: 40rpx;
  background-color: #ff6d00;
  mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z'/%3E%3C/svg%3E");
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z'/%3E%3C/svg%3E");
  -webkit-mask-size: contain;
  mask-size: contain;
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-position: center;
  mask-position: center;
}

.location-detail-section {
  padding: 30rpx;
  background-color: #ffffff;
  border-top: 20rpx solid #f5f5f5;
}

.location-detail-header {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #333;
}

.location-detail-input-wrap {
  margin-bottom: 20rpx;
  position: relative;
}

.location-detail-input {
  width: 100%;
  padding: 20rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  background-color: #ffffff;
  color: #333;
  font-size: 28rpx;
  height: 85rpx;
  box-sizing: border-box;
}

.location-detail-tips {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 30rpx;
}

.location-action-btns {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.location-map-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 12rpx 24rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  font-size: 26rpx;
  border: none;
  color: #666;
}

.location-btn {
  background-color: #ff6d00;
  color: #fff;
  text-align: center;
  padding: 25rpx 0;
  border-radius: 10rpx;
  font-size: 30rpx;
  font-weight: bold;
}

.hot-cities-section {
  padding: 20rpx 30rpx;
}

.hot-cities-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.hot-cities-list {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 10rpx;
}

.hot-city-item {
  width: 140rpx;
  height: 70rpx;
  line-height: 70rpx;
  text-align: center;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  font-size: 26rpx;
  color: #333;
}

.location-divider {
  height: 20rpx;
  background-color: #f5f5f5;
  margin-bottom: 10rpx;
}

.province-list-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  padding: 20rpx 30rpx;
}
</style>
