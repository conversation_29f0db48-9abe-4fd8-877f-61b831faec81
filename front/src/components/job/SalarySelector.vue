<template>
  <view class="salary-selector">
    <!-- 结算方式 -->
    <view class="form-item border-bottom pb-20rpx mb-30rpx">
      <view class="form-label text-info mb-20rpx">
        <text class="text-28rpx">结算方式</text>
      </view>
      <view class="salary-type-picker">
        <picker
          :value="salaryTypeIndex"
          :range="salaryTypeOptions"
          range-key="name"
          @change="handleSalaryTypeChange"
        >
          <view class="form-selector flex justify-between items-center">
            <text
              class="text-30rpx"
              :class="salaryType ? 'text-base' : 'text-grey'"
            >
              {{ salaryType || "请选择结算方式" }}
            </text>
            <text class="i-carbon-chevron-right text-grey"></text>
          </view>
        </picker>
      </view>
    </view>

    <!-- 薪资范围 -->
    <view class="form-item" v-if="salaryType">
      <view class="form-label mb-20rpx">
        <text class="text-28rpx">薪资范围</text>
      </view>

      <!-- 日薪/周薪/时薪直接输入模式 -->
      <view
        v-if="showDirectInput"
        class="direct-salary-input flex items-center"
      >
        <input
          type="digit"
          :value="customSalaryMin"
          @input="(e: any) => emit('update:customSalaryMin', e.detail.value)"
          class="form-input text-30rpx"
          :placeholder="`请输入${salaryType}`"
          placeholder-class="placeholder-style"
        />
        <text class="unit-text ml-10rpx">{{ salaryUnit }}</text>
      </view>

      <!-- 月薪/年薪选择模式 -->
      <template v-else>
        <view
          v-if="isCustomSalary"
          class="custom-salary-input flex items-center"
        >
          <input
            type="digit"
            :value="customSalaryMin"
            @input="(e: any) => emit('update:customSalaryMin', e.detail.value)"
            class="salary-input text-30rpx"
            placeholder="最低"
            placeholder-class="placeholder-style"
          />
          <text class="mx-10rpx">-</text>
          <input
            type="digit"
            :value="customSalaryMax"
            @input="(e: any) => emit('update:customSalaryMax', e.detail.value)"
            class="salary-input text-30rpx"
            placeholder="最高"
            placeholder-class="placeholder-style"
          />
          <text class="unit-text ml-10rpx">{{ salaryUnit }}</text>
          <view
            class="switch-salary-btn ml-20rpx"
            @tap="toggleCustomSalary(false)"
            >切换常用</view
          >
        </view>
        <view v-else class="flex justify-between items-center">
          <view class="flex-1">
            <view class="salary-range-picker">
              <picker
                :value="salaryRangeIndex"
                :range="salaryRanges"
                range-key="name"
                @change="handleSalaryRangeChange"
              >
                <view class="form-selector flex justify-between items-center">
                  <text
                    class="text-30rpx"
                    :class="salaryText ? 'text-base' : 'text-grey'"
                  >
                    {{ salaryText || "请选择薪资范围" }}
                  </text>
                  <text class="i-carbon-chevron-right text-grey"></text>
                </view>
              </picker>
            </view>
          </view>
          <view
            class="switch-salary-btn ml-20rpx"
            @tap="toggleCustomSalary(true)"
            >自定义</view
          >
        </view>
      </template>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { salaryTypeOptions, salaryRanges } from "@/constants/static/job";

const props = defineProps({
  salaryType: {
    type: String,
    default: "",
  },
  salaryText: {
    type: String,
    default: "",
  },
  customSalaryMin: {
    type: [String, Number],
    default: "",
  },
  customSalaryMax: {
    type: [String, Number],
    default: "",
  },
  isCustomSalary: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits([
  "update:salaryType",
  "update:salaryText",
  "update:customSalaryMin",
  "update:customSalaryMax",
  "update:isCustomSalary",
]);

// 当前选择的结算方式索引
const salaryTypeIndex = computed(() => {
  if (!props.salaryType) return 0;
  const index = salaryTypeOptions.findIndex(
    (item) => item.name === props.salaryType
  );
  return index >= 0 ? index : 0;
});

// 处理结算方式选择变化
const handleSalaryTypeChange = (e: any) => {
  const index = e.detail.value;
  const selected = salaryTypeOptions[index];
  emit("update:salaryType", selected.name);

  // 当切换结算方式时，清空薪资范围
  emit("update:salaryText", "");

  // 如果选择了日结或小时结，清空自定义薪资并设置为直接输入模式
  if (selected.name === "日结" || selected.name === "小时结") {
    emit("update:customSalaryMin", "");
    emit("update:customSalaryMax", "");
    // 不需要显式设置isCustomSalary，因为我们使用showDirectInput来控制显示
  }
};

// 结算方式对应的单位
const salaryUnit = computed(() => {
  switch (props.salaryType) {
    case "月结":
      return "元/月";
    case "周结":
      return "元/周";
    case "日结":
      return "元/天";
    case "小时结":
      return "元/小时";
    default:
      return "";
  }
});

// 是否显示薪资直接输入框（日薪、周薪或时薪时直接输入）
const showDirectInput = computed(() => {
  return (
    props.salaryType === "日结" ||
    props.salaryType === "周结" ||
    props.salaryType === "小时结"
  );
});

// 薪资范围选择器索引
const salaryRangeIndex = computed(() => {
  if (!props.salaryText) return 0;
  const index = salaryRanges.findIndex(
    (item) => item.name === props.salaryText
  );
  return index >= 0 ? index : 0;
});

// 处理薪资范围选择变化
const handleSalaryRangeChange = (e: any) => {
  const index = e.detail.value;
  const selected = salaryRanges[index];
  emit("update:salaryText", selected.name);
};

// 切换自定义薪资
const toggleCustomSalary = (value: boolean) => {
  emit("update:isCustomSalary", value);
};

// 监听customSalaryMin变化
watch(
  () => props.customSalaryMin,
  (val) => {
    emit("update:customSalaryMin", val);
  }
);

// 监听customSalaryMax变化
watch(
  () => props.customSalaryMax,
  (val) => {
    emit("update:customSalaryMax", val);
  }
);
</script>

<style lang="scss" scoped>
.form-item {
  margin-bottom: 30rpx;
}

.border-bottom {
  border-bottom: 1rpx solid $border-color;
}

.form-selector {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  background-color: $bg-search;
  border-radius: 8rpx;
  border: 1rpx solid $border-color;
}

.form-input {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  padding: 0 20rpx;
  background-color: $bg-search;
  border-radius: 8rpx;
  border: 1rpx solid $border-color;
  box-sizing: border-box;
}

.salary-input {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  padding: 0 20rpx;
  background-color: $bg-search;
  border-radius: 8rpx;
  border: 1rpx solid $border-color;
  font-size: 28rpx;
}

.direct-salary-input {
  width: 100%;
}

.direct-input {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  padding: 0 20rpx;
  border: 1rpx solid $border-color;
  border-radius: 8rpx;
  background-color: $bg-search;
  font-size: 28rpx;
}

.unit-text {
  font-size: 26rpx;
  color: $text-info;
}

.switch-salary-btn {
  font-size: 24rpx;
  color: $primary;
  padding: 8rpx 16rpx;
  border-radius: 30rpx;
  background-color: rgba(255, 109, 0, 0.1);
}

.picker-value {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  background-color: $bg-search;
  border-radius: 8rpx;
}
</style>
