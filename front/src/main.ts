import App from "./App.vue";
import 'virtual:uno.css'
import uvUI from '@climblee/uv-ui'
import { createSSRApp } from "vue";

import store from "./stores";
import PageLayout from "./components/PageLayout.vue";
import NetworkImage from "./components/NetworkImage.vue";
import CustomNavBar from "./components/CustomNavBar.vue";
import Card from "./components/common/Card.vue";
import Tag from "./components/common/Tag.vue";
// 导入ThorUI配置
import tuiConfig from "./components/thorui/tui-config/index.js";

export function createApp() {
  const app = createSSRApp(App);

  // 确保Pinia在所有组件之前初始化
  app.use(store);
  app.use(uvUI);

  // 挂载ThorUI全局配置到uni对象上
  uni.$tui = tuiConfig;

  // 初始化 Pinia store 实例，以便在拦截器中使用

  // 配置全局导航拦截器
  // const interceptorFunc = (args: any) => {
  //   // 检查页面是否在白名单中
  //   const url = args.url.split('?')[0];
  //   if (whiteList.includes(url)) {
  //     return true;
  //   }

  //   // 检查用户是否已同意隐私协议
  //   if (!appStore.hasAgreedPrivacy) {
  //     console.log('拦截：用户未同意隐私协议');
  //     appStore.setPrivacyPopupVisible(true);
  //     return false; // 中断跳转
  //   }

  //   return true; // 放行
  // }
  // uni.addInterceptor('navigateTo', { invoke: interceptorFunc });
  // uni.addInterceptor('redirectTo', { invoke: interceptorFunc });
  // uni.addInterceptor('reLaunch', { invoke: interceptorFunc });
  // uni.addInterceptor('switchTab', { invoke: interceptorFunc });

  // 全局注册组件
  app.component('PageLayout', PageLayout);
  app.component('NetworkImage', NetworkImage);
  app.component('CustomNavBar', CustomNavBar);
  app.component('Card', Card);
  app.component('Tag', Tag);
  return {
    app,
  };
}
