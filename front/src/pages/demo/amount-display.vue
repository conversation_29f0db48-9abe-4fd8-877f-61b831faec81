<template>
  <view class="demo-page">
    <view class="demo-header">
      <text class="demo-title">AmountDisplay 金额显示组件</text>
      <text class="demo-subtitle">简化版本 - 三种大小，金额数字默认红色</text>
    </view>

    <!-- 基础用法 -->
    <view class="demo-section">
      <text class="section-title">基础用法</text>
      <view class="demo-item">
        <text class="demo-label">默认样式：</text>
        <AmountDisplay :amount="10000" unit="元/月" />
      </view>
    </view>

    <!-- 三种大小 -->
    <view class="demo-section">
      <text class="section-title">三种大小</text>
      <view class="demo-item">
        <text class="demo-label">小号 (small)：</text>
        <AmountDisplay :amount="5000" unit="元/月" size="small" />
      </view>
      <view class="demo-item">
        <text class="demo-label">中号 (medium)：</text>
        <AmountDisplay :amount="8000" unit="元/月" size="medium" />
      </view>
      <view class="demo-item">
        <text class="demo-label">大号 (large)：</text>
        <AmountDisplay :amount="12000" unit="元/月" size="large" />
      </view>
    </view>

    <!-- 自定义颜色 -->
    <view class="demo-section">
      <text class="section-title">自定义颜色</text>
      <view class="demo-item">
        <text class="demo-label">绿色金额：</text>
        <AmountDisplay :amount="15000" unit="元/月" value-color="#52C41A" />
      </view>
      <view class="demo-item">
        <text class="demo-label">蓝色符号：</text>
        <AmountDisplay :amount="8000" unit="元/月" symbol-color="#1890FF" />
      </view>
      <view class="demo-item">
        <text class="demo-label">灰色单位：</text>
        <AmountDisplay :amount="6000" unit="元/月" unit-color="#999999" />
      </view>
    </view>

    <!-- 不同货币和单位 -->
    <view class="demo-section">
      <text class="section-title">不同货币和单位</text>
      <view class="demo-item">
        <text class="demo-label">美元：</text>
        <AmountDisplay :amount="1500" currency="$" unit="/month" />
      </view>
      <view class="demo-item">
        <text class="demo-label">时薪：</text>
        <AmountDisplay :amount="50" unit="元/小时" />
      </view>
      <view class="demo-item">
        <text class="demo-label">日薪：</text>
        <AmountDisplay :amount="300" unit="元/天" />
      </view>
    </view>

    <!-- 数字格式 -->
    <view class="demo-section">
      <text class="section-title">数字格式</text>
      <view class="demo-item">
        <text class="demo-label">带小数：</text>
        <AmountDisplay :amount="1234.56" :decimal="2" unit="元" />
      </view>
      <view class="demo-item">
        <text class="demo-label">无千分位：</text>
        <AmountDisplay :amount="12345" :separator="false" unit="元" />
      </view>
      <view class="demo-item">
        <text class="demo-label">大数字：</text>
        <AmountDisplay :amount="1234567" unit="元" />
      </view>
    </view>

    <!-- 业务场景 -->
    <view class="demo-section">
      <text class="section-title">业务场景示例</text>
      <view class="demo-item">
        <text class="demo-label">职位薪资：</text>
        <AmountDisplay :amount="15000" unit="元/月" size="large" />
      </view>
      <view class="demo-item">
        <text class="demo-label">兼职时薪：</text>
        <AmountDisplay :amount="80" unit="元/小时" size="medium" />
      </view>
      <view class="demo-item">
        <text class="demo-label">项目报酬：</text>
        <AmountDisplay :amount="5000" unit="元/项目" size="small" />
      </view>
    </view>

    <!-- 点击事件测试 -->
    <view class="demo-section">
      <text class="section-title">点击事件测试</text>
      <view class="demo-item">
        <text class="demo-label">点击金额：</text>
        <AmountDisplay 
          :amount="clickAmount" 
          unit="元/月" 
          @click="handleAmountClick"
          style="cursor: pointer; background: #f5f5f5; padding: 10rpx; border-radius: 8rpx;"
        />
      </view>
      <text class="click-result">{{ clickMessage }}</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from "vue";
import AmountDisplay from "@/components/common/AmountDisplay.vue";

const clickAmount = ref(9999);
const clickMessage = ref("点击上方金额试试");

const handleAmountClick = (amount: number | string) => {
  clickMessage.value = `你点击了金额：${amount}`;
  clickAmount.value = Math.floor(Math.random() * 20000) + 1000;
};
</script>

<style scoped>
.demo-page {
  padding: 40rpx;
  background: #f8f9fa;
  min-height: 100vh;
}

.demo-header {
  text-align: center;
  margin-bottom: 60rpx;
}

.demo-title {
  font-size: 48rpx;
  font-weight: bold;
  color: var(--text-base);
  display: block;
  margin-bottom: 20rpx;
}

.demo-subtitle {
  font-size: 28rpx;
  color: var(--text-secondary);
  display: block;
}

.demo-section {
  background: white;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--text-base);
  display: block;
  margin-bottom: 30rpx;
  border-bottom: 2rpx solid #e8e8e8;
  padding-bottom: 20rpx;
}

.demo-item {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  padding: 20rpx;
  background: #fafafa;
  border-radius: 12rpx;
}

.demo-item:last-child {
  margin-bottom: 0;
}

.demo-label {
  font-size: 28rpx;
  color: var(--text-secondary);
  width: 200rpx;
  flex-shrink: 0;
}

.click-result {
  font-size: 24rpx;
  color: var(--text-info);
  margin-top: 20rpx;
  display: block;
  text-align: center;
  padding: 20rpx;
  background: #e6f7ff;
  border-radius: 8rpx;
}
</style>