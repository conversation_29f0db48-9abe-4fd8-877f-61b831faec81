<template>
  <view class="gig-calendar-page">
    <!-- 顶部统计卡片 -->
    <view class="stats-section">
      <view class="stats-card glass-card">
        <view v-if="isLoadingMonthlyStats && !monthlyStats" class="loading-skeleton">
          <view class="skeleton-item"></view>
          <view class="skeleton-item"></view>
          <view class="skeleton-item"></view>
        </view>
        <template v-else>
          <view class="stat-item">
            <text class="stat-number">{{ totalGigs }}</text>
            <text class="stat-label">本月零工</text>
          </view>
          <view class="stat-divider"></view>
          <view class="stat-item">
            <text class="stat-number">{{ completedGigs }}</text>
            <text class="stat-label">已完成</text>
          </view>
          <view class="stat-divider"></view>
          <view class="stat-item">
            <text class="stat-number">{{ pendingGigs }}</text>
            <text class="stat-label">待进行</text>
          </view>
        </template>
      </view>
    </view>

    <!-- 周日期选择器 -->
    <view class="week-selector-section">
      <view class="section-title">
        <text class="title-text">选择日期</text>
        <text class="current-month">{{ currentMonth }}</text>
      </view>
      <view class="week-calendar-wrapper glass-card">
        <tui-week-date
          :value="selectedDate"
          :activeColor="'white'"
          :activeBackground="'linear-gradient(135deg, #2673FF, #667eea)'"
          @click="onDateSelect"
        />
      </view>
    </view>

    <!-- 当日零工列表 -->
    <view class="daily-gigs-section">
      <view class="section-header">
        <view class="section-title">
          <text class="title-text">{{ formatDisplayDate(selectedDate) }}</text>
          <text class="gig-count">{{ dailyGigs.length }}个零工</text>
        </view>
        <view v-if="dailyGigs.length > 0" class="filter-tabs">
          <view
            v-for="status in statusTabs"
            :key="status.value"
            class="filter-tab"
            :class="{ active: activeStatus === status.value }"
            @tap="setActiveStatus(status.value)"
          >
            <text>{{ status.label }}</text>
          </view>
        </view>
      </view>

      <view class="gigs-list">
        <view v-if="isLoadingDailyGigs && dailyGigs.length === 0" class="loading-gigs">
          <view class="gig-skeleton" v-for="i in 3" :key="i">
            <view class="skeleton-header">
              <view class="skeleton-title"></view>
              <view class="skeleton-salary"></view>
            </view>
            <view class="skeleton-content">
              <view class="skeleton-line"></view>
              <view class="skeleton-line"></view>
            </view>
          </view>
        </view>
        <view v-else-if="filteredGigs.length > 0" class="gig-items">
          <view
            v-for="item in filteredGigs"
            :key="`${item.type}-${item.id}`"
            class="gig-item glass-card"
            :class="{ 'application-item': item.type === 'application' }"
            @tap="goToGigDetail(item.gig_id || item.id)"
          >
            <view class="gig-header">
              <view class="gig-title-area">
                <text class="gig-title">{{ item.displayTitle }}</text>
                <view class="gig-status" :class="item.displayStatus">
                  <text>{{ getStatusText(item.displayStatus) }}</text>
                </view>
              </view>
              <view class="gig-salary">
                <text class="salary-amount">{{
                  formatGigSalary(item.displaySalary, item.displayUnit)
                }}</text>
              </view>
            </view>

            <view class="gig-info">
              <view class="info-item">
                <text class="i-carbon-time info-icon"></text>
                <text class="info-text">{{ item.displayTime }}</text>
              </view>
              <view class="info-item">
                <text class="i-carbon-location info-icon"></text>
                <text class="info-text">{{ item.displayLocation }}</text>
              </view>
              <view class="info-item">
                <text class="i-carbon-user info-icon"></text>
                <text class="info-text">{{ item.displayContact }}</text>
              </view>
              <view v-if="item.type === 'application'" class="info-item">
                <text class="i-carbon-tag info-icon"></text>
                <text class="info-text">我的申请</text>
              </view>
            </view>

            <view class="gig-footer">
              <view class="gig-tags">
                <text
                  v-for="tag in item.displayTags"
                  :key="tag"
                  class="gig-tag"
                  >{{ tag }}</text
                >
              </view>
              <view class="gig-actions">
                <!-- 我的申请的操作按钮 -->
                <template v-if="item.type === 'application'">
                  <view
                    v-if="item.displayStatus === GigApplicationStatus.Pending"
                    class="action-btn warning"
                    @tap.stop="withdrawApplication(item.id)"
                  >
                    <text>撤销申请</text>
                  </view>
                  <view
                    v-else-if="
                      item.displayStatus === GigApplicationStatus.Confirmed
                    "
                    class="action-btn danger"
                    @tap.stop="cancelApplication(item.id)"
                  >
                    <text>取消预约</text>
                  </view>
                  <view
                    v-if="item.displayStatus === GigApplicationStatus.Confirmed"
                    class="action-btn secondary"
                    @tap.stop="contactEmployer(item)"
                  >
                    <text>联系雇主</text>
                  </view>
                  <view
                    v-else-if="
                      item.displayStatus === GigApplicationStatus.Completed
                    "
                    class="action-btn success"
                    @tap.stop="reviewGig(item.gig_id)"
                  >
                    <text>去评价</text>
                  </view>
                </template>

                <!-- 我的发布的操作按钮 -->
                <template v-else-if="item.type === 'published'">
                  <view
                    v-if="item.displayStatus === GigStatus.Recruiting"
                    class="action-btn primary"
                    @tap.stop="manageGig(item.id)"
                  >
                    <text>管理</text>
                  </view>
                  <view
                    v-else-if="item.displayStatus === GigStatus.InProgress"
                    class="action-btn secondary"
                    @tap.stop="viewApplicants(item.id)"
                  >
                    <text>查看申请</text>
                  </view>
                  <view
                    v-else-if="item.displayStatus === GigStatus.Completed"
                    class="action-btn success"
                    @tap.stop="viewResult(item.id)"
                  >
                    <text>查看结果</text>
                  </view>
                </template>
              </view>
            </view>
          </view>
        </view>

        <view v-else class="empty-state">
          <view class="empty-icon">
            <text class="i-carbon-calendar"></text>
          </view>
          <text class="empty-title">{{ getEmptyTitle() }}</text>
          <text class="empty-desc">{{ getEmptyDesc() }}</text>
          <view class="empty-action" @tap="goToGigList">
            <text>去看看其他零工</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from "vue";
import { useGigCalendar } from "@/services/gig";
import { GigStatus, GigApplicationStatus } from "@/constants/gig";
import { formatDateSmart, formatTimeRange } from "@/utils/core/date";
import {
  formatGigSalary,
  getSalaryUnitText,
  getGigStatusDetails,
  getGigApplicationStatusDetails,
} from "@/utils";
import {
  showErrorToast,
  showSuccessToast,
} from "@/utils/ui/feedback";
import { navigateTo, buildUrl } from "@/utils/ui/navigation";
import { standardCall } from "@/utils/network/helpers";
import dayjs from "dayjs";

// 数据状态
const selectedDate = ref(dayjs().format("YYYY-MM-DD"));
const activeStatus = ref("all");

// 使用 useGigCalendar composable
const {
  monthlyStats,
  totalGigs,
  completedGigs,
  pendingGigs,
  isLoadingMonthlyStats,
  monthlyStatsError,
  fetchMonthlyStats,
  dailyGigs,
  dailyApplications,
  isLoadingDailyGigs,
  dailyGigsError,
  fetchDailyGigs,
} = useGigCalendar();

// 状态标签页
const statusTabs = ref([
  { label: "全部", value: "all" },
  { label: "我的申请", value: "applications" },
  { label: "我的发布", value: "published" },
  { label: "待确认", value: GigApplicationStatus.Pending },
  { label: "已确认", value: GigApplicationStatus.Confirmed },
  { label: "已完成", value: GigApplicationStatus.Completed },
]);

// 加载状态计算属性
const loading = computed(() => isLoadingMonthlyStats.value || isLoadingDailyGigs.value);

// 计算属性
const currentMonth = computed(() => {
  return dayjs(selectedDate.value).format("YYYY年MM月");
});

// dailyGigs 和 dailyApplications 现在由 composable 提供

const allDailyItems = computed(() => {
  const items = [];

  // 添加发布的零工（标记为published类型）
  dailyGigs.value.forEach((gig) => {
    items.push({
      ...gig,
      type: "published",
      displayTitle: gig.title,
      displayStatus: gig.status,
      displaySalary: gig.salary,
      displayUnit: gig.salary_unit,
      displayTime: formatGigTime(gig.start_time, gig.end_time),
      displayLocation: gig.address_name,
      displayContact: gig.contact_name,
      displayTags: gig.tags || [],
    });
  });

  // 添加申请的零工（标记为application类型）
  dailyApplications.value.forEach((app) => {
    if (app.gig_info) {
      items.push({
        ...app,
        type: "application",
        displayTitle: app.gig_info.title,
        displayStatus: app.status,
        displaySalary: app.gig_info.salary,
        displayUnit: app.gig_info.salary_unit,
        displayTime: formatGigTime(
          app.gig_info.start_time,
          app.gig_info.end_time
        ),
        displayLocation: app.gig_info.address_name,
        displayContact: app.gig_info.contact_name,
        displayTags: app.gig_info.tags || [],
      });
    }
  });

  return items;
});

const filteredGigs = computed(() => {
  if (activeStatus.value === "all") {
    return allDailyItems.value;
  } else if (activeStatus.value === "applications") {
    return allDailyItems.value.filter((item) => item.type === "application");
  } else if (activeStatus.value === "published") {
    return allDailyItems.value.filter((item) => item.type === "published");
  }
  return allDailyItems.value.filter(
    (item) => item.displayStatus === activeStatus.value
  );
});

// totalGigs, completedGigs, pendingGigs 现在由 composable 提供

// 方法
const onDateSelect = (dateInfo: any) => {
  selectedDate.value = dateInfo.date;
  activeStatus.value = "all"; // 重置状态筛选
};

const setActiveStatus = (status: string) => {
  activeStatus.value = status;
};

// 监听日期变化，自动获取数据
watch(
  selectedDate,
  async (newDate) => {
    if (newDate) {
      await fetchDailyGigs(newDate);
    }
  },
  { immediate: true }
);

// 监听月份变化，自动获取月度统计
watch(
  () => dayjs(selectedDate.value).format("YYYY-MM"),
  async (newMonth) => {
    if (newMonth) {
      const [year, month] = newMonth.split("-");
      await fetchMonthlyStats(parseInt(year), parseInt(month));
    }
  },
  { immediate: true }
);

const formatDisplayDate = (date: string) => {
  return formatDateSmart(date);
};

const getStatusText = (status: string) => {
  // 首先检查是否是GigStatus
  if (Object.values(GigStatus).includes(status as GigStatus)) {
    return getGigStatusDetails(status as GigStatus).text;
  }

  // 然后检查是否是GigApplicationStatus
  if (
    Object.values(GigApplicationStatus).includes(status as GigApplicationStatus)
  ) {
    return getGigApplicationStatusDetails(
      status as GigApplicationStatus,
      "seeker"
    ).text;
  }

  return status;
};

const getSalaryUnit = (unit: number) => {
  return getSalaryUnitText(unit);
};

const formatGigTime = (startTime: string, endTime: string) => {
  return formatTimeRange(startTime, endTime);
};

const getEmptyTitle = () => {
  if (activeStatus.value === "all") {
    return "暂无零工安排";
  }

  if (activeStatus.value === "applications") {
    return "暂无申请记录";
  }

  if (activeStatus.value === "published") {
    return "暂无发布记录";
  }

  const statusMap = {
    [GigApplicationStatus.Pending]: "没有待确认的零工",
    [GigApplicationStatus.Confirmed]: "没有已确认的零工",
    [GigApplicationStatus.Completed]: "没有已完成的零工",
  };
  return statusMap[activeStatus.value] || "暂无数据";
};

const getEmptyDesc = () => {
  if (activeStatus.value === "all") {
    return "这一天还没有零工安排，去看看其他机会吧";
  }
  return "切换其他状态查看或选择其他日期";
};

const confirmGig = async (gigId: number) => {
  try {
    // 这里需要调用确认零工的API
    // await confirmGigApplication(gigId);
    showSuccessToast("确认成功");
    // 重新获取当日数据
    await fetchDailyGigs(selectedDate.value);
  } catch (err) {
    showErrorToast("确认失败");
  }
};

// 撤销申请
const withdrawApplication = async (applicationId: number) => {
  const success = await standardCall(async () => {
    // 调用撤销申请的API
    // return await updateApplicationStatus({ application_id: applicationId, new_status: 'Withdrawn' });

    // 临时模拟API调用
    return new Promise((resolve) => setTimeout(resolve, 500));
  });

  if (success) {
    showSuccessToast("申请已撤销");
    await fetchDailyGigs(selectedDate.value);
  }
};

// 取消预约
const cancelApplication = async (applicationId: number) => {
  const success = await standardCall(async () => {
    // 调用取消预约的API
    // return await updateApplicationStatus({ application_id: applicationId, new_status: 'Cancelled' });

    // 临时模拟API调用
    return new Promise((resolve) => setTimeout(resolve, 500));
  });

  if (success) {
    showSuccessToast("预约已取消");
    await fetchDailyGigs(selectedDate.value);
  }
};

// 评价零工
const reviewGig = (gigId: number) => {
  navigateTo(buildUrl("/pages/gig/review", { gigId }));
};

// 管理零工
const manageGig = (gigId: number) => {
  navigateTo(buildUrl("/pages/gig/manage", { id: gigId }));
};

// 查看申请者
const viewApplicants = (gigId: number) => {
  navigateTo(buildUrl("/pages/gig/applicants", { gigId }));
};

const contactEmployer = (gig: any) => {
  uni.showModal({
    title: "联系雇主",
    content: `是否要联系 ${gig.employerName}？`,
    success: (res) => {
      if (res.confirm) {
        showSuccessToast("正在跳转到聊天");
      }
    },
  });
};

const viewResult = (gigId: number) => {
  showSuccessToast("查看工作结果");
};

const goToGigDetail = (gigId: number) => {
  navigateTo(buildUrl("/pages/gig/detail/index", { id: gigId }));
};

const goToGigList = () => {
  navigateTo("/pages/gig/index");
};

onMounted(() => {
  // 页面加载时的初始化逻辑
});
</script>

<style lang="scss" scoped>
.gig-calendar-page {
  min-height: 100vh;
  background-color: var(--bg-page);
  padding-bottom: var(--spacing-20);
}

.stats-section {
  padding: var(--spacing-16);
  margin-top: var(--spacing-10);
}

.stats-card {
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: var(--spacing-16);
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 var(--spacing-4) var(--spacing-16) rgba(0, 0, 0, 0.08);
}

.stat-item {
  text-align: center;
  flex: 1;

  .stat-number {
    display: block;
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-base);
    margin-bottom: var(--spacing-4);
  }

  .stat-label {
    font-size: var(--font-size-sm);
    color: var(--text-info);
  }
}

.stat-divider {
  width: 1rpx;
  height: var(--spacing-20);
  background-color: var(--border-color);
  margin: 0 var(--spacing-12);
}

.week-selector-section {
  padding: 0 var(--spacing-16) var(--spacing-16);
}

.section-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-10);

  .title-text {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-bold);
    color: var(--text-base);
  }

  .current-month {
    font-size: var(--font-size-sm);
    color: var(--text-info);
  }

  .gig-count {
    font-size: var(--font-size-sm);
    color: var(--text-info);
  }
}

.week-calendar-wrapper {
  border-radius: var(--radius-lg);
  box-shadow: 0 var(--spacing-4) var(--spacing-16) rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.daily-gigs-section {
  padding: 0 var(--spacing-16);
}

.section-header {
  margin-bottom: var(--spacing-12);
}

.filter-tabs {
  display: flex;
  gap: var(--spacing-8);
  margin-top: var(--spacing-8);
}

.filter-tab {
  padding: var(--spacing-6) var(--spacing-12);
  border-radius: var(--radius-base);
  background-color: var(--bg-tag);
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  transition: all 0.3s ease;

  &.active {
    background-color: var(--primary);
    color: var(--text-inverse);
  }
}

.gigs-list {
  display: flex;
  flex-direction: column;
}

.gig-items {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-12);
}

.gig-item {
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: var(--spacing-16);
  box-shadow: 0 var(--spacing-4) var(--spacing-16) rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
  }
}

.gig-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-8);
}

.gig-title-area {
  flex: 1;
  margin-right: var(--spacing-8);
}

.gig-title {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-bold);
  color: var(--text-base);
  margin-bottom: var(--spacing-4);
  line-height: 1.4;
}

.gig-status {
  display: inline-block;
  padding: var(--spacing-4) var(--spacing-8);
  border-radius: var(--radius-base);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);

  // 申请状态样式
  &.pending {
    background-color: var(--bg-warning-light);
    color: var(--text-yellow);
  }

  &.confirmed {
    background-color: var(--bg-success-light);
    color: var(--text-green);
  }

  &.completed {
    background-color: var(--bg-tag);
    color: var(--text-secondary);
  }

  &.rejected {
    background-color: var(--bg-danger-light);
    color: var(--text-red);
  }

  &.cancelled {
    background-color: var(--bg-danger-light);
    color: var(--text-red);
  }

  &.withdrawn {
    background-color: var(--bg-tag);
    color: var(--text-disable);
  }

  &.no_show {
    background-color: var(--bg-danger-light);
    color: var(--text-red);
  }

  // 零工状态样式
  &.recruiting {
    background-color: var(--bg-primary-light);
    color: var(--primary);
  }

  &.in_progress {
    background-color: var(--bg-info-light);
    color: var(--text-blue);
  }

  &.paused {
    background-color: var(--bg-warning-light);
    color: var(--text-yellow);
  }

  &.locked {
    background-color: var(--bg-info-light);
    color: var(--text-blue);
  }

  &.closed {
    background-color: var(--bg-danger-light);
    color: var(--text-red);
  }

  &.draft {
    background-color: var(--bg-tag);
    color: var(--text-disable);
  }
}

.gig-salary {
  text-align: right;

  .salary-amount {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-bold);
    color: var(--primary);
  }

  .salary-unit {
    font-size: var(--font-size-xs);
    color: var(--text-info);
    margin-left: var(--spacing-4);
  }
}

.gig-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
  margin-bottom: var(--spacing-12);
}

.info-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-6);
}

.info-icon {
  font-size: var(--font-size-base);
  color: var(--text-info);
  width: var(--font-size-base);
  flex-shrink: 0;
}

.info-text {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.gig-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--spacing-12);
  padding-top: var(--spacing-12);
  border-top: 1rpx solid var(--border-color);
}

.gig-tags {
  display: flex;
  gap: var(--spacing-6);
  flex: 1;
  flex-wrap: wrap;
}

.gig-tag {
  padding: var(--spacing-4) var(--spacing-8);
  background-color: var(--bg-tag);
  color: var(--text-secondary);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
}

.gig-actions {
  display: flex;
  gap: var(--spacing-8);
}

.action-btn {
  padding: var(--spacing-6) var(--spacing-12);
  border-radius: var(--radius-base);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  transition: all 0.3s ease;

  &.primary {
    background-color: var(--primary);
    color: var(--text-inverse);
  }

  &.secondary {
    background-color: var(--bg-tag);
    color: var(--text-secondary);
  }

  &.success {
    background-color: var(--bg-success-light);
    color: var(--text-green);
  }

  &.warning {
    background-color: var(--bg-warning-light);
    color: var(--text-yellow);
  }

  &.danger {
    background-color: var(--bg-danger-light);
    color: var(--text-red);
  }

  &:active {
    transform: scale(0.95);
  }
}

.application-item {
  border-left: 4rpx solid var(--primary);

  .gig-title {
    color: var(--primary);
  }
}

.empty-state {
  text-align: center;
  padding: var(--spacing-40) var(--spacing-20);
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  box-shadow: 0 var(--spacing-4) var(--spacing-16) rgba(0, 0, 0, 0.08);
  margin-top: var(--spacing-16);
}

.empty-icon {
  font-size: 120rpx;
  color: var(--text-disable);
  margin-bottom: var(--spacing-16);
}

.empty-title {
  display: block;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--text-base);
  margin-bottom: var(--spacing-8);
}

.empty-desc {
  display: block;
  font-size: var(--font-size-base);
  color: var(--text-info);
  margin-bottom: var(--spacing-20);
  line-height: 1.5;
}

.empty-action {
  display: inline-block;
  padding: var(--spacing-8) var(--spacing-16);
  background-color: var(--primary);
  color: var(--text-inverse);
  border-radius: var(--radius-base);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
    background-color: var(--primary-700);
  }
}

// 骨架屏样式
.loading-skeleton {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-16);
}

.skeleton-item {
  flex: 1;
  text-align: center;

  &::before {
    content: "";
    display: block;
    width: 60rpx;
    height: 40rpx;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: var(--radius-sm);
    margin: 0 auto var(--spacing-8);
  }

  &::after {
    content: "";
    display: block;
    width: 80rpx;
    height: 24rpx;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: var(--radius-sm);
    margin: 0 auto;
  }
}

.loading-gigs {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-12);
}

.gig-skeleton {
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: var(--spacing-16);
  box-shadow: 0 var(--spacing-4) var(--spacing-16) rgba(0, 0, 0, 0.08);
}

.skeleton-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-12);
}

.skeleton-title {
  width: 200rpx;
  height: 32rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: var(--radius-sm);
}

.skeleton-salary {
  width: 80rpx;
  height: 32rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: var(--radius-sm);
}

.skeleton-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-8);
}

.skeleton-line {
  height: 24rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: var(--radius-sm);

  &:first-child {
    width: 100%;
  }

  &:last-child {
    width: 60%;
  }
}

@keyframes skeleton-loading {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}
</style>
