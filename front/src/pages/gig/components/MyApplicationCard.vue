<template>
  <view class="application-card bg-white rounded-xl shadow-md p-4 space-y-3">
    <!-- 卡片头部：零工标题和状态 -->
    <view class="header flex justify-between items-start">
      <text class="title font-bold text-lg flex-1 pr-3">{{
        application.gig_info?.title
      }}</text>
      <view
        class="status-badge text-sm font-bold"
        :style="{ color: statusDetails.color }"
      >
        {{ statusDetails.text }}
      </view>
    </view>

    <!-- 薪酬和时间信息 -->
    <view class="info-body space-y-2 text-gray-600">
      <view class="info-row flex items-center">
        <view class="i-carbon-wallet text-lg mr-2" />
        <text class="salary font-semibold text-primary">{{
          formattedSalary
        }}</text>
      </view>
      <view class="info-row flex items-center">
        <view class="i-carbon-time text-lg mr-2" />
        <text>{{ formattedWorkTime }}</text>
      </view>
      <view class="info-row flex items-center">
        <view class="i-carbon-location text-lg mr-2" />
        <text>{{ application.gig_info?.address_name }}</text>
      </view>
    </view>

    <!-- 分割线 -->
    <u-line />

    <!-- 操作按钮 -->
    <view class="actions flex justify-end items-center space-x-3">
      <u-button
        v-if="canWithdraw"
        type="warning"
        size="small"
        plain
        shape="circle"
        text="撤销申请"
        @click="$emit('withdraw', application.id)"
      />
      <u-button
        v-if="canCancel"
        type="error"
        size="small"
        plain
        shape="circle"
        text="取消预约"
        @click="$emit('cancel', application.id)"
      />
      <u-button
        v-if="canContact"
        type="primary"
        size="small"
        shape="circle"
        text="联系对方"
        @click="$emit('contact', application)"
      />
      <u-button
        v-if="canReview"
        type="success"
        size="small"
        shape="circle"
        text="去评价"
        @click="$emit('review', application.gig_id)"
      />
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed, defineProps, defineEmits } from "vue";
import type { GigApplication } from "@/types/gig";
import { formatGigSalary, getGigApplicationStatusDetails } from "@/utils/business/gig";
import { formatTimeRange } from "@/utils/core/date";

interface Props {
  application: GigApplication;
}

const props = defineProps<Props>();
const emit = defineEmits(["withdraw", "cancel", "contact", "review"]);

// 状态详情
const statusDetails = computed(() => {
  return getGigApplicationStatusDetails(props.application.status, "seeker");
});

// 格式化薪酬
const formattedSalary = computed(() => {
  const gig = props.application.gig_info;
  if (!gig) return "";
  return formatGigSalary(gig.salary, gig.salary_unit);
});

// 格式化工作时间
const formattedWorkTime = computed(() => {
  const gig = props.application.gig_info;
  if (!gig) return "";
  return formatTimeRange(gig.start_time, gig.end_time);
});

// --- 操作按钮的计算属性 ---
const canWithdraw = computed(
  () => props.application.status === GigApplicationStatus.Pending
);
const canCancel = computed(
  () => props.application.status === GigApplicationStatus.Confirmed
);
const canContact = computed(
  () => props.application.status === GigApplicationStatus.Confirmed
);
const canReview = computed(
  () => props.application.status === GigApplicationStatus.Completed
);
</script>

<style lang="scss" scoped>
// 使用原子化CSS，无需额外样式
</style>
