<template>
  <tui-bottom-popup :zIndex="1002" :maskZIndex="1001" :show="show">
    <view class="apply-modal">
      <!-- 极简主义头部 -->
      <view class="modal-header">
        <view class="header-grip"></view>
        <view class="header-title">申请职位</view>
        <view class="close-button" @tap="handleClose">
          <text class="i-carbon-close"></text>
        </view>
      </view>

      <!-- 内容区域 -->
      <scroll-view class="modal-content" scroll-y>
        <!-- 职位信息预览 -->
        <view class="gig-preview" v-if="gig">
          <text class="gig-title">{{ gig.title }}</text>
          <view class="gig-meta">
            <text class="gig-salary">{{ formatSalary(gig.salary, gig.salaryUnit) }}</text>
            <text class="gig-divider">·</text>
            <text class="gig-location">{{ gig.location }}</text>
          </view>
        </view>

        <!-- 用户身份卡片 -->
        <view class="identity-card">
          <view class="avatar-section">
            <view class="user-avatar">
              <text class="avatar-initial">{{ userStore.user?.nickname?.charAt(0) || '?' }}</text>
            </view>
            <view class="user-info" v-if="isUserInfoComplete">
              <text class="user-name">{{ userStore.user?.nickname }}</text>
              <text class="user-phone">{{ userStore.user?.phone }}</text>
            </view>
            <view class="user-info" v-else>
              <text class="user-name">完善信息</text>
              <text class="user-phone">需要真实姓名和手机号</text>
            </view>
          </view>
          <view class="edit-action" @tap="goToProfile" v-if="!isUserInfoComplete">
            <text class="edit-text">完善</text>
            <text class="i-carbon-chevron-right"></text>
          </view>
        </view>

        <!-- 申请表单 -->
        <view class="form-section">
          <view class="form-group">
            <label class="form-label">相关经验</label>
            <view class="radio-group">
              <view 
                class="radio-option"
                :class="{ active: formData.hasExperience === false }"
                @tap="selectExperience(false)"
              >
                <view class="radio-indicator"></view>
                <text class="radio-label">无相关经验</text>
              </view>
              <view 
                class="radio-option"
                :class="{ active: formData.hasExperience === true }"
                @tap="selectExperience(true)"
              >
                <view class="radio-indicator"></view>
                <text class="radio-label">有相关经验</text>
              </view>
            </view>
          </view>

          <view class="form-group">
            <label class="form-label">
              补充说明
              <text class="optional">选填</text>
            </label>
            <view class="textarea-container">
              <textarea
                v-model="formData.remarks"
                class="form-textarea"
                placeholder="分享您的相关经验或想法..."
                maxlength="200"
                auto-height
              />
              <view class="char-counter">
                <text class="counter-text">{{ formData.remarks.length }}/200</text>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>

      <!-- 底部操作栏 -->
      <view class="action-bar">
        <view class="agreement-row">
          <view 
            class="checkbox"
            :class="{ checked: formData.agreedToTerms }"
            @tap="toggleAgreement"
          >
            <text v-if="formData.agreedToTerms" class="i-carbon-checkmark"></text>
          </view>
          <text class="agreement-text">
            我已阅读并同意
            <text class="agreement-link" @tap.stop="showAgreement">服务协议</text>
          </text>
        </view>
        <view class="submit-container">
          <button
            class="submit-button"
            :class="{ disabled: !canSubmit }"
            :disabled="!canSubmit || isSubmitting"
            @tap="handleSubmit"
          >
            <text v-if="!isSubmitting" class="button-text">提交申请</text>
            <view v-else class="loading-spinner">
              <view class="spinner"></view>
            </view>
          </button>
        </view>
      </view>
    </view>
  </tui-bottom-popup>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import type { Gig } from "@/types/gig";
import { formatGigSalary } from "@/utils/business/gig";
import { formatTimeRange } from "@/utils/core/date";
import { validatePhone, validateRequired } from "@/utils/core/validation";
import { showErrorToast, showSuccessToast, showLoading, hideLoading } from "@/utils/ui/feedback";
import { useUserStore } from "@/stores/user";

interface Props {
  show: boolean;
  gig?: Gig | null;
}

interface ApplyFormData {
  hasExperience: boolean;
  remarks: string;
  agreedToTerms: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
  gig: null,
});

const emit = defineEmits<{
  close: [];
  submit: [data: ApplyFormData];
}>();

const userStore = useUserStore();
const popup = ref();

// 表单数据
const formData = ref<ApplyFormData>({
  hasExperience: false,
  remarks: "",
  agreedToTerms: false,
});

// 表单验证错误
const errors = ref<Record<string, string>>({});

// 提交状态
const isSubmitting = ref(false);

// 经验选项
const experienceOptions = [
  { label: "无相关经验", value: false },
  { label: "有相关经验", value: true },
];

// 监听显示状态，控制popup显示和重置表单
watch(
  () => props.show,
  (newShow) => {
    if (newShow) {
      resetForm();
      popup.value?.open();
    } else {
      popup.value?.close();
    }
  }
);

// 计算属性
const canSubmit = computed(() => {
  // 检查用户信息是否完整
  const hasUserInfo = userStore.user?.nickname && userStore.user?.phone;
  return (
    hasUserInfo &&
    formData.value.agreedToTerms &&
    !isSubmitting.value
  );
});

// 检查用户信息是否完整
const isUserInfoComplete = computed(() => {
  return userStore.user?.nickname && userStore.user?.phone;
});

// 方法
const resetForm = () => {
  formData.value = {
    hasExperience: false,
    remarks: "",
    agreedToTerms: false,
  };
  errors.value = {};
  isSubmitting.value = false;
};

const formatSalary = (salary?: number, unit?: number) => {
  if (!salary || !unit) return "面议";
  return formatGigSalary(salary, unit);
};

const formatWorkTime = (startTime?: string, endTime?: string) => {
  if (!startTime || !endTime) return "时间待定";
  return formatTimeRange(startTime, endTime);
};

// 跳转到个人资料页面
const goToProfile = () => {
  uni.navigateTo({
    url: '/pages/user/profile'
  });
};

// 表单交互方法
const selectExperience = (value: boolean) => {
  formData.value.hasExperience = value;
};

const toggleAgreement = () => {
  formData.value.agreedToTerms = !formData.value.agreedToTerms;
};

const showAgreement = () => {
  uni.showModal({
    title: "申请服务协议",
    content:
      "这里是申请服务协议的内容，包含用户权利义务、隐私保护、服务条款等相关规定。",
    showCancel: false,
    confirmText: "知道了",
  });
};

// 事件处理
const handleClose = () => {
  if (isSubmitting.value) return;
  emit("close");
};

const handleSubmit = async () => {
  if (!canSubmit.value) return;

  // 检查用户信息完整性
  if (!isUserInfoComplete.value) {
    showErrorToast("请先完善个人信息");
    return;
  }

  try {
    isSubmitting.value = true;
    
    // 显示全局loading，防止用户进行其他操作
    showLoading("提交申请中...", true);

    // 构造完整的申请数据
    const submitData = {
      contactName: userStore.user?.nickname || '',
      contactPhone: userStore.user?.phone || '',
      hasExperience: formData.value.hasExperience,
      remarks: formData.value.remarks.trim(),
      agreedToTerms: formData.value.agreedToTerms,
    };

    // 模拟提交延迟
    await new Promise((resolve) => setTimeout(resolve, 1000));

    emit("submit", submitData);
    showSuccessToast("申请提交成功");
  } catch (error: any) {
    console.error("申请提交失败:", error);
    
    // 根据错误类型显示不同的错误信息
    if (error?.response?.status === 500) {
      showErrorToast("服务器内部错误，请稍后重试");
    } else if (error?.response?.status === 400) {
      showErrorToast(error?.response?.data?.message || "请求参数错误");
    } else if (error?.response?.status === 401) {
      showErrorToast("请先登录");
    } else if (error?.response?.status === 403) {
      showErrorToast("没有权限进行此操作");
    } else if (error?.message) {
      showErrorToast(error.message);
    } else {
      showErrorToast("提交失败，请重试");
    }
  } finally {
    isSubmitting.value = false;
    // 确保隐藏全局loading
    hideLoading();
  }
};
</script>

<style scoped lang="scss">
/* 设计系统变量 */
:root {
  --color-primary: #ff6d00;
  --color-primary-light: #ff8a50;
  --color-background: #ffffff;
  --color-surface: #f8f9fa;
  --color-surface-hover: #f1f3f4;
  --color-text-primary: #212121;
  --color-text-secondary: #757575;
  --color-text-hint: #bdbdbd;
  --color-border: #e0e0e0;
  --color-border-focus: #ff6d00;
  --color-success: #4caf50;
  --color-warning: #ff9800;
  --color-error: #f44336;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --radius-sm: 8rpx;
  --radius-md: 12rpx;
  --radius-lg: 16rpx;
  --radius-xl: 20rpx;
  --radius-full: 50%;
}

/* 主容器 */
.apply-modal {
  display: flex;
  flex-direction: column;
  height: 85vh;
  max-height: 600rpx;
  background: var(--color-background);
  border-radius: var(--radius-xl) var(--radius-xl) 0 0;
  box-shadow: var(--shadow-lg);
  overflow: hidden;
}

/* 极简头部 */
.modal-header {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32rpx 0;
  border-bottom: 1rpx solid var(--color-border);
}

.header-grip {
  position: absolute;
  top: 12rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background: var(--color-border);
  border-radius: var(--radius-full);
}

.header-title {
  font-size: 34rpx;
  font-weight: 600;
  color: var(--color-text-primary);
  letter-spacing: -0.02em;
}

.close-button {
  position: absolute;
  right: 24rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-full);
  background: var(--color-surface);
  transition: all 0.2s ease;

  &:active {
    transform: translateY(-50%) scale(0.9);
    background: var(--color-surface-hover);
  }

  .i-carbon-close {
    font-size: 20rpx;
    color: var(--color-text-secondary);
  }
}

/* 内容区域 */
.modal-content {
  flex: 1;
  padding: 32rpx 24rpx;
  overflow-y: auto;
}

/* 职位预览 */
.gig-preview {
  margin-bottom: 32rpx;
  padding: 24rpx;
  background: var(--color-surface);
  border-radius: var(--radius-lg);
  border: 1rpx solid var(--color-border);
}

.gig-title {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: var(--color-text-primary);
  margin-bottom: 8rpx;
  line-height: 1.4;
}

.gig-meta {
  display: flex;
  align-items: center;
  gap: 12rpx;
  font-size: 24rpx;
  color: var(--color-text-secondary);
}

.gig-salary {
  font-weight: 600;
  color: var(--color-primary);
}

.gig-divider {
  color: var(--color-text-hint);
}

.gig-location {
  color: var(--color-text-secondary);
}

/* 身份卡片 */
.identity-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  margin-bottom: 32rpx;
  background: var(--color-surface);
  border-radius: var(--radius-lg);
  border: 1rpx solid var(--color-border);
  transition: all 0.2s ease;

  &:hover {
    background: var(--color-surface-hover);
  }
}

.avatar-section {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.user-avatar {
  width: 48rpx;
  height: 48rpx;
  border-radius: var(--radius-full);
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-light) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.avatar-initial {
  font-size: 20rpx;
  font-weight: 700;
  color: white;
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 2rpx;
}

.user-name {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--color-text-primary);
  line-height: 1.3;
}

.user-phone {
  font-size: 24rpx;
  color: var(--color-text-secondary);
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
}

.edit-action {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  border-radius: var(--radius-md);
  background: var(--color-primary);
  transition: all 0.2s ease;

  &:active {
    transform: scale(0.95);
  }
}

.edit-text {
  font-size: 24rpx;
  font-weight: 500;
  color: white;
}

.i-carbon-chevron-right {
  font-size: 16rpx;
  color: white;
}

/* 表单区域 */
.form-section {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.form-label {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--color-text-primary);
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.optional {
  font-size: 24rpx;
  font-weight: 400;
  color: var(--color-text-hint);
  margin-left: 8rpx;
}

/* 单选按钮组 */
.radio-group {
  display: flex;
  gap: 12rpx;
}

.radio-option {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 20rpx 24rpx;
  background: var(--color-surface);
  border: 1rpx solid var(--color-border);
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
  cursor: pointer;

  &:active {
    transform: scale(0.98);
  }

  &.active {
    background: rgba(255, 109, 0, 0.05);
    border-color: var(--color-primary);
  }
}

.radio-indicator {
  width: 20rpx;
  height: 20rpx;
  border: 2rpx solid var(--color-border);
  border-radius: var(--radius-full);
  position: relative;
  transition: all 0.2s ease;

  .radio-option.active & {
    border-color: var(--color-primary);
    background: var(--color-primary);
  }
}

.radio-label {
  font-size: 28rpx;
  font-weight: 500;
  color: var(--color-text-primary);
}

/* 文本域 */
.textarea-container {
  position: relative;
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 20rpx;
  background: var(--color-surface);
  border: 1rpx solid var(--color-border);
  border-radius: var(--radius-md);
  font-size: 28rpx;
  color: var(--color-text-primary);
  line-height: 1.5;
  resize: none;
  transition: all 0.2s ease;

  &:focus {
    border-color: var(--color-primary);
    background: var(--color-background);
    outline: none;
  }

  &::placeholder {
    color: var(--color-text-hint);
  }
}

.char-counter {
  position: absolute;
  bottom: 12rpx;
  right: 12rpx;
}

.counter-text {
  font-size: 22rpx;
  color: var(--color-text-hint);
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
}

/* 底部操作栏 */
.action-bar {
  padding: 24rpx;
  background: var(--color-background);
  border-top: 1rpx solid var(--color-border);
}

.agreement-row {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.checkbox {
  width: 20rpx;
  height: 20rpx;
  border: 2rpx solid var(--color-border);
  border-radius: var(--radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  &.checked {
    background: var(--color-primary);
    border-color: var(--color-primary);
  }

  .i-carbon-checkmark {
    font-size: 14rpx;
    color: white;
  }
}

.agreement-text {
  font-size: 24rpx;
  color: var(--color-text-secondary);
  line-height: 1.4;
}

.agreement-link {
  color: var(--color-primary);
  font-weight: 500;
}

.submit-container {
  margin-top: 16rpx;
}

.submit-button {
  width: 100%;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-primary);
  border: none;
  border-radius: var(--radius-lg);
  font-size: 30rpx;
  font-weight: 600;
  color: white;
  transition: all 0.2s ease;
  cursor: pointer;

  &:active {
    transform: scale(0.98);
    background: #e65100;
  }

  &.disabled {
    background: var(--color-text-hint);
    cursor: not-allowed;

    &:active {
      transform: none;
    }
  }
}

.button-text {
  font-size: 30rpx;
  font-weight: 600;
  color: white;
  letter-spacing: 0.02em;
}

.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
}

.spinner {
  width: 24rpx;
  height: 24rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-top: 2rpx solid white;
  border-radius: var(--radius-full);
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 375px) {
  .apply-modal {
    height: 90vh;
  }
  
  .modal-content {
    padding: 24rpx 20rpx;
  }
  
  .radio-group {
    flex-direction: column;
  }
  
  .radio-option {
    width: 100%;
  }
}

@media (max-height: 667px) {
  .apply-modal {
    height: 95vh;
  }
  
  .modal-content {
    padding: 20rpx;
  }
  
  .form-section {
    gap: 24rpx;
  }
}

/* 微交互优化 */
* {
  -webkit-tap-highlight-color: transparent;
}

.apply-modal {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}
</style>
