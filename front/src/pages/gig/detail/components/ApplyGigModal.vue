<template>
  <tui-bottom-popup :zIndex="1002" :maskZIndex="1001" :show="show">
    <view class="apply-modal">
      <!-- 现代化头部设计 -->
      <view class="modal-header">
        <view class="header-grip"></view>
        <view class="header-content">
          <text class="header-title">申请职位</text>
          <text class="header-subtitle">完善信息，提升成功率</text>
        </view>
        <view class="close-button" @tap="handleClose">
          <text class="i-carbon-close"></text>
        </view>
      </view>

      <!-- 内容区域 -->
      <scroll-view class="modal-content" scroll-y enhanced :show-scrollbar="false">
        <!-- 用户身份卡片 - 重新设计 -->
        <view class="identity-section">
          <view class="identity-card" :class="{ 'incomplete': !isUserInfoComplete }">
            <view class="card-content">
              <view class="avatar-container">
                <view class="user-avatar" :class="{ 'incomplete': !isUserInfoComplete }">
                  <text class="avatar-initial">{{ userStore.user?.nickname?.charAt(0) || '?' }}</text>
                  <view class="avatar-status" v-if="isUserInfoComplete">
                    <text class="i-carbon-checkmark-filled"></text>
                  </view>
                </view>
              </view>

              <view class="user-details">
                <view class="user-info" v-if="isUserInfoComplete">
                  <text class="user-name">{{ userStore.user?.nickname }}</text>
                  <text class="user-phone">{{ userStore.user?.phone }}</text>
                  <view class="verified-badge">
                    <text class="i-carbon-checkmark-filled"></text>
                    <text class="badge-text">已认证</text>
                  </view>
                </view>
                <view class="user-info incomplete" v-else>
                  <text class="user-name">完善个人信息</text>
                  <text class="user-hint">真实姓名和手机号有助于提升申请成功率</text>
                </view>
              </view>
            </view>

            <view class="card-action" @tap="goToProfile" v-if="!isUserInfoComplete">
              <view class="action-button">
                <text class="action-text">立即完善</text>
                <text class="i-carbon-arrow-right"></text>
              </view>
            </view>
          </view>
        </view>

        <!-- 申请表单 - 现代化设计 -->
        <view class="form-section">
          <!-- 经验选择 -->
          <view class="form-group">
            <view class="form-header">
              <text class="form-label">相关经验</text>
              <text class="form-description">请选择您的经验情况</text>
            </view>
            <view class="radio-container">
              <view
                class="radio-card"
                :class="{ 'selected': formData.hasExperience === false }"
                @tap="selectExperience(false)"
              >
                <view class="radio-indicator">
                  <view class="radio-dot" v-if="formData.hasExperience === false"></view>
                </view>
                <view class="radio-content">
                  <text class="radio-title">无相关经验</text>
                  <text class="radio-subtitle">愿意学习新技能</text>
                </view>
              </view>
              <view
                class="radio-card"
                :class="{ 'selected': formData.hasExperience === true }"
                @tap="selectExperience(true)"
              >
                <view class="radio-indicator">
                  <view class="radio-dot" v-if="formData.hasExperience === true"></view>
                </view>
                <view class="radio-content">
                  <text class="radio-title">有相关经验</text>
                  <text class="radio-subtitle">具备相关技能和经验</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 补充说明 -->
          <view class="form-group">
            <view class="form-header">
              <text class="form-label">
                补充说明
                <text class="optional-tag">选填</text>
              </text>
              <text class="form-description">分享您的想法或相关经验</text>
            </view>
            <view class="textarea-wrapper">
              <textarea
                v-model="formData.remarks"
                class="form-textarea"
                placeholder="例如：我对这个工作很感兴趣，虽然没有相关经验，但我学习能力强，愿意快速上手..."
                maxlength="200"
                auto-height
                :focus="textareaFocused"
                @focus="textareaFocused = true"
                @blur="textareaFocused = false"
              />
              <view class="textarea-footer">
                <view class="char-counter">
                  <text class="counter-text">{{ formData.remarks.length }}/200</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>

      <!-- 底部操作栏 - 重新设计 -->
      <view class="action-section">
        <view class="agreement-container">
          <view
            class="custom-checkbox"
            :class="{ 'checked': formData.agreedToTerms }"
            @tap="toggleAgreement"
          >
            <text v-if="formData.agreedToTerms" class="i-carbon-checkmark"></text>
          </view>
          <text class="agreement-text">
            我已阅读并同意
            <text class="agreement-link" @tap.stop="showAgreement">《服务协议》</text>
          </text>
        </view>

        <view class="submit-wrapper">
          <button
            class="submit-btn"
            :class="{
              'disabled': !canSubmit,
              'loading': isSubmitting
            }"
            :disabled="!canSubmit || isSubmitting"
            @tap="handleSubmit"
          >
            <view v-if="!isSubmitting" class="btn-content">
              <text class="btn-text">提交申请</text>
            </view>
            <view v-else class="btn-loading">
              <view class="loading-spinner">
                <view class="spinner-ring"></view>
              </view>
              <text class="loading-text">提交中...</text>
            </view>
          </button>
        </view>
      </view>
    </view>
  </tui-bottom-popup>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import type { Gig } from "@/types/gig";
import { showErrorToast, showSuccessToast, showLoading, hideLoading } from "@/utils/ui/feedback";
import { useUserStore } from "@/stores/user";

interface Props {
  show: boolean;
  gig?: Gig | null;
}

interface ApplyFormData {
  hasExperience: boolean;
  remarks: string;
  agreedToTerms: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
  gig: null,
});

const emit = defineEmits<{
  close: [];
  submit: [data: ApplyFormData];
}>();

const userStore = useUserStore();

// 表单数据
const formData = ref<ApplyFormData>({
  hasExperience: false,
  remarks: "",
  agreedToTerms: false,
});

// UI状态
const isSubmitting = ref(false);
const textareaFocused = ref(false);

// 监听显示状态，重置表单
watch(
  () => props.show,
  (newShow) => {
    if (newShow) {
      resetForm();
    }
  }
);

// 计算属性
const canSubmit = computed(() => {
  // 检查用户信息是否完整
  const hasUserInfo = userStore.user?.nickname && userStore.user?.phone;
  return (
    hasUserInfo &&
    formData.value.agreedToTerms &&
    !isSubmitting.value
  );
});

// 检查用户信息是否完整
const isUserInfoComplete = computed(() => {
  return userStore.user?.nickname && userStore.user?.phone;
});

// 方法
const resetForm = () => {
  formData.value = {
    hasExperience: false,
    remarks: "",
    agreedToTerms: false,
  };
  isSubmitting.value = false;
  textareaFocused.value = false;
};

// 跳转到个人资料页面
const goToProfile = () => {
  uni.navigateTo({
    url: '/pages/user/profile'
  });
};

// 表单交互方法
const selectExperience = (value: boolean) => {
  formData.value.hasExperience = value;
};

const toggleAgreement = () => {
  formData.value.agreedToTerms = !formData.value.agreedToTerms;
};

const showAgreement = () => {
  uni.showModal({
    title: "申请服务协议",
    content:
      "这里是申请服务协议的内容，包含用户权利义务、隐私保护、服务条款等相关规定。",
    showCancel: false,
    confirmText: "知道了",
  });
};

// 事件处理
const handleClose = () => {
  if (isSubmitting.value) return;
  emit("close");
};

const handleSubmit = async () => {
  if (!canSubmit.value) return;

  // 检查用户信息完整性
  if (!isUserInfoComplete.value) {
    showErrorToast("请先完善个人信息");
    return;
  }

  try {
    isSubmitting.value = true;
    
    // 显示全局loading，防止用户进行其他操作
    showLoading("提交申请中...", true);

    // 构造完整的申请数据
    const submitData = {
      contactName: userStore.user?.nickname || '',
      contactPhone: userStore.user?.phone || '',
      hasExperience: formData.value.hasExperience,
      remarks: formData.value.remarks.trim(),
      agreedToTerms: formData.value.agreedToTerms,
    };

    // 模拟提交延迟
    await new Promise((resolve) => setTimeout(resolve, 1000));

    emit("submit", submitData);
    showSuccessToast("申请提交成功");
  } catch (error: any) {
    console.error("申请提交失败:", error);
    
    // 根据错误类型显示不同的错误信息
    if (error?.response?.status === 500) {
      showErrorToast("服务器内部错误，请稍后重试");
    } else if (error?.response?.status === 400) {
      showErrorToast(error?.response?.data?.message || "请求参数错误");
    } else if (error?.response?.status === 401) {
      showErrorToast("请先登录");
    } else if (error?.response?.status === 403) {
      showErrorToast("没有权限进行此操作");
    } else if (error?.message) {
      showErrorToast(error.message);
    } else {
      showErrorToast("提交失败，请重试");
    }
  } finally {
    isSubmitting.value = false;
    // 确保隐藏全局loading
    hideLoading();
  }
};
</script>

<style scoped lang="scss">
/* ==================== 现代化移动端设计系统 ==================== */

/* 主容器 - 采用现代化设计 */
.apply-modal {
  display: flex;
  flex-direction: column;
  height: 88vh;
  max-height: 1200rpx;
  background: var(--bg-card);
  border-radius: var(--radius-xl) var(--radius-xl) 0 0;
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.12);
  overflow: hidden;
  position: relative;
}

/* ==================== 现代化头部设计 ==================== */
.modal-header {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-16) var(--spacing-24) var(--spacing-20);
  background: linear-gradient(135deg, var(--bg-card) 0%, #fafbfc 100%);
  border-bottom: 1rpx solid var(--border-color);
}

.header-grip {
  position: absolute;
  top: var(--spacing-12);
  left: 50%;
  transform: translateX(-50%);
  width: 64rpx;
  height: 6rpx;
  background: var(--text-grey);
  border-radius: var(--radius-full);
  opacity: 0.6;
}

.header-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-4);
  margin-top: var(--spacing-8);
}

.header-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-base);
  letter-spacing: -0.02em;
  line-height: var(--line-height-tight);
}

.header-subtitle {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: var(--font-weight-regular);
}

.close-button {
  position: absolute;
  right: var(--spacing-20);
  top: var(--spacing-20);
  width: 56rpx;
  height: 56rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-full);
  background: var(--bg-tag);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:active {
    transform: scale(0.92);
    background: var(--text-info);
  }

  .i-carbon-close {
    font-size: 32rpx;
    color: var(--text-secondary);
  }
}

/* ==================== 内容区域 ==================== */
.modal-content {
  flex: 1;
  padding: var(--spacing-24);
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

/* ==================== 用户身份卡片 - 现代化设计 ==================== */
.identity-section {
  margin-bottom: var(--spacing-24);
}

.identity-card {
  display: flex;
  flex-direction: column;
  background: var(--bg-card);
  border-radius: var(--radius-xl);
  border: 2rpx solid var(--border-color);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.04);

  &.incomplete {
    border-color: var(--primary-200);
    background: linear-gradient(135deg, var(--bg-card) 0%, var(--bg-primary-light) 100%);
  }

  &:active {
    transform: translateY(-2rpx);
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
  }
}

.card-content {
  display: flex;
  align-items: center;
  padding: var(--spacing-20);
  gap: var(--spacing-16);
}

.avatar-container {
  position: relative;
  flex-shrink: 0;
}

.user-avatar {
  width: 96rpx;
  height: 96rpx;
  border-radius: var(--radius-full);
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-400) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow: 0 4rpx 16rpx rgba(255, 109, 0, 0.2);

  &.incomplete {
    background: linear-gradient(135deg, var(--text-grey) 0%, var(--text-info) 100%);
    box-shadow: 0 4rpx 16rpx rgba(161, 161, 170, 0.2);
  }
}

.avatar-initial {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--text-inverse);
  text-transform: uppercase;
}

.avatar-status {
  position: absolute;
  bottom: -4rpx;
  right: -4rpx;
  width: 32rpx;
  height: 32rpx;
  background: var(--text-green);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3rpx solid var(--bg-card);

  .i-carbon-checkmark-filled {
    font-size: 20rpx;
    color: var(--text-inverse);
  }
}

.user-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-8);
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);

  &.incomplete {
    gap: var(--spacing-4);
  }
}

.user-name {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
  color: var(--text-base);
  line-height: var(--line-height-tight);
}

.user-phone {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
  font-variant-numeric: tabular-nums;
}

.user-hint {
  font-size: var(--font-size-xs);
  color: var(--text-info);
  line-height: var(--line-height-normal);
}

.verified-badge {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  padding: var(--spacing-4) var(--spacing-8);
  background: var(--bg-success-light);
  border-radius: var(--radius-lg);
  align-self: flex-start;

  .i-carbon-checkmark-filled {
    font-size: 20rpx;
    color: var(--text-green);
  }
}

.badge-text {
  font-size: var(--font-size-xs);
  color: var(--text-green);
  font-weight: var(--font-weight-medium);
}

.card-action {
  border-top: 1rpx solid var(--border-color);
  padding: var(--spacing-16) var(--spacing-20);
}

.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-8);
  padding: var(--spacing-12) var(--spacing-20);
  background: var(--primary);
  border-radius: var(--radius-lg);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:active {
    transform: scale(0.96);
    background: var(--primary-700);
  }
}

.action-text {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-inverse);
}

.i-carbon-arrow-right {
  font-size: 24rpx;
  color: var(--text-inverse);
}

/* ==================== 表单区域 - 现代化设计 ==================== */
.form-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-32);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-16);
}

.form-header {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-8);
}

.form-label {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
  color: var(--text-base);
  display: flex;
  align-items: center;
  gap: var(--spacing-8);
}

.form-description {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  line-height: var(--line-height-normal);
}

.optional-tag {
  display: inline-flex;
  align-items: center;
  padding: 2rpx var(--spacing-6);
  background: var(--bg-tag);
  color: var(--text-info);
  font-size: 20rpx;
  font-weight: var(--font-weight-regular);
  border-radius: var(--radius-sm);
  margin-left: var(--spacing-8);
}

/* ==================== 单选按钮组 - 卡片式设计 ==================== */
.radio-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-12);
}

.radio-card {
  display: flex;
  align-items: center;
  gap: var(--spacing-16);
  padding: var(--spacing-20);
  background: var(--bg-card);
  border: 2rpx solid var(--border-color);
  border-radius: var(--radius-lg);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;

  &:active {
    transform: scale(0.98);
  }

  &.selected {
    background: var(--bg-primary-light);
    border-color: var(--primary);
    box-shadow: 0 4rpx 16rpx rgba(255, 109, 0, 0.1);
  }
}

.radio-indicator {
  width: 40rpx;
  height: 40rpx;
  border: 3rpx solid var(--border-color);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  flex-shrink: 0;

  .radio-card.selected & {
    border-color: var(--primary);
    background: var(--primary);
  }
}

.radio-dot {
  width: 16rpx;
  height: 16rpx;
  background: var(--text-inverse);
  border-radius: var(--radius-full);
  transform: scale(0);
  animation: radioSelect 0.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

@keyframes radioSelect {
  to {
    transform: scale(1);
  }
}

.radio-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
  flex: 1;
}

.radio-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--text-base);
  line-height: var(--line-height-tight);
}

.radio-subtitle {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  line-height: var(--line-height-normal);
}

/* ==================== 文本域 - 现代化设计 ==================== */
.textarea-wrapper {
  position: relative;
}

.form-textarea {
  width: 100%;
  min-height: 200rpx;
  padding: var(--spacing-20);
  background: var(--bg-input);
  border: 2rpx solid var(--border-color);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  color: var(--text-base);
  line-height: var(--line-height-normal);
  resize: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;

  &:focus {
    border-color: var(--primary);
    background: var(--bg-card);
    outline: none;
    box-shadow: 0 0 0 6rpx rgba(255, 109, 0, 0.1);
  }

  &::placeholder {
    color: var(--text-grey);
    line-height: var(--line-height-normal);
  }
}

.textarea-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: var(--spacing-8);
}

.char-counter {
  display: flex;
  align-items: center;
}

.counter-text {
  font-size: var(--font-size-xs);
  color: var(--text-info);
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
  font-variant-numeric: tabular-nums;
}

/* ==================== 底部操作栏 - 现代化设计 ==================== */
.action-section {
  padding: var(--spacing-20) var(--spacing-24) var(--spacing-24);
  background: var(--bg-card);
  border-top: 1rpx solid var(--border-color);
  backdrop-filter: blur(20rpx);
  -webkit-backdrop-filter: blur(20rpx);
}

.agreement-container {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-12);
  margin-bottom: var(--spacing-20);
  padding: var(--spacing-16);
  background: var(--bg-tag);
  border-radius: var(--radius-lg);
}

.custom-checkbox {
  width: 36rpx;
  height: 36rpx;
  border: 2rpx solid var(--border-color);
  border-radius: var(--radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  flex-shrink: 0;
  cursor: pointer;

  &:active {
    transform: scale(0.9);
  }

  &.checked {
    background: var(--primary);
    border-color: var(--primary);
    box-shadow: 0 2rpx 8rpx rgba(255, 109, 0, 0.3);
  }

  .i-carbon-checkmark {
    font-size: 24rpx;
    color: var(--text-inverse);
    transform: scale(0);
    transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  &.checked .i-carbon-checkmark {
    transform: scale(1);
  }
}

.agreement-text {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: var(--line-height-normal);
  flex: 1;
}

.agreement-link {
  color: var(--primary);
  font-weight: var(--font-weight-medium);
  text-decoration: none;

  &:active {
    opacity: 0.7;
  }
}

.submit-wrapper {
  position: relative;
}

.submit-btn {
  width: 100%;
  height: 96rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-400) 100%);
  border: none;
  border-radius: var(--radius-xl);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  box-shadow: 0 8rpx 24rpx rgba(255, 109, 0, 0.3);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  &:active {
    transform: scale(0.98);
    box-shadow: 0 4rpx 16rpx rgba(255, 109, 0, 0.4);

    &::before {
      left: 100%;
    }
  }

  &.disabled {
    background: var(--text-grey);
    box-shadow: none;
    cursor: not-allowed;

    &:active {
      transform: none;
    }

    &::before {
      display: none;
    }
  }

  &.loading {
    background: var(--primary-600);
    cursor: not-allowed;

    &:active {
      transform: none;
    }
  }
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-text {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
  color: var(--text-inverse);
  letter-spacing: 0.02em;
}

.btn-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-12);
}

.loading-spinner {
  position: relative;
}

.spinner-ring {
  width: 32rpx;
  height: 32rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
  border-top: 3rpx solid var(--text-inverse);
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: var(--font-size-sm);
  color: var(--text-inverse);
  font-weight: var(--font-weight-medium);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 750rpx) {
  .apply-modal {
    height: 92vh;
    max-height: none;
  }

  .modal-content {
    padding: var(--spacing-20);
  }

  .card-content {
    padding: var(--spacing-16);
  }

  .user-avatar {
    width: 80rpx;
    height: 80rpx;
  }

  .avatar-initial {
    font-size: var(--font-size-base);
  }

  .form-section {
    gap: var(--spacing-24);
  }

  .radio-container {
    gap: var(--spacing-10);
  }

  .radio-card {
    padding: var(--spacing-16);
  }
}

@media (max-height: 1334rpx) {
  .apply-modal {
    height: 95vh;
  }

  .modal-content {
    padding: var(--spacing-16);
  }

  .form-section {
    gap: var(--spacing-20);
  }

  .form-group {
    gap: var(--spacing-12);
  }
}

/* ==================== 微交互和动画优化 ==================== */
* {
  -webkit-tap-highlight-color: transparent;
  box-sizing: border-box;
}

.apply-modal {
  animation: modalSlideUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: bottom;
}

@keyframes modalSlideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 滚动条样式 */
.modal-content::-webkit-scrollbar {
  display: none;
}

.modal-content {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

/* 焦点状态优化 */
.form-textarea:focus,
.custom-checkbox:focus,
.radio-card:focus,
.submit-btn:focus {
  outline: none;
}

/* 触摸反馈优化 */
.radio-card:active,
.custom-checkbox:active,
.action-button:active,
.close-button:active {
  transition-duration: 0.1s;
}

/* 加载状态动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.submit-btn.loading {
  animation: pulse 2s ease-in-out infinite;
}

/* 深色模式适配预留 */
@media (prefers-color-scheme: dark) {
  /* 深色模式样式可以在这里添加 */
}

/* 高对比度模式适配 */
@media (prefers-contrast: high) {
  .identity-card,
  .radio-card,
  .form-textarea {
    border-width: 3rpx;
  }

  .submit-btn {
    border: 2rpx solid var(--primary-700);
  }
}

/* 减少动画模式适配 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
</style>
