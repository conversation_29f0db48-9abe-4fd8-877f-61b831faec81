<template>
  <!-- 操作按钮区域 -->
  <view class="actions-container">
    <!-- 分享按钮 -->
    <view class="action-item share-btn" @tap="handleShare">
      <text class="action-icon i-carbon-share"></text>
      <text class="action-text">分享</text>
    </view>

    <!-- 主要操作按钮组 -->
    <view class="main-actions">
      <!-- 求职者视角的按钮 -->
      <template v-if="userRole === 'seeker'">
        <!-- 立即申请按钮 -->
        <view
          v-if="showApplyButton"
          class="primary-btn apply-btn"
          :class="{ disabled: isApplyDisabled }"
          @tap="handleApply"
        >
          <text class="btn-text">{{ applyButtonText }}</text>
        </view>

        <!-- 联系雇主按钮 -->
        <view
          v-if="showContactButton"
          class="secondary-btn contact-btn"
          @tap="handleContact"
        >
          <text class="btn-text">联系雇主</text>
        </view>

        <!-- 撤销申请按钮 -->
        <view
          v-if="showWithdrawButton"
          class="danger-btn withdraw-btn"
          @tap="handleWithdraw"
        >
          <text class="btn-text">撤销申请</text>
        </view>
      </template>

      <!-- 招聘者视角的按钮 -->
      <template v-if="userRole === 'recruiter'">
        <!-- 管理申请按钮 -->
        <view
          v-if="showManageButton"
          class="primary-btn manage-btn"
          @tap="handleManageApplications"
        >
          <text class="btn-text">管理申请</text>
          <text v-if="applicationCount > 0" class="count-badge">{{
            applicationCount
          }}</text>
        </view>

        <!-- 更多操作按钮 -->
        <view class="secondary-btn more-actions-btn" @tap="showMoreActions">
          <text class="btn-text">更多操作</text>
          <text class="i-carbon-chevron-up more-icon"></text>
        </view>
      </template>

      <!-- 访客视角（未登录） -->
      <template v-if="userRole === 'guest'">
        <view class="primary-btn login-btn" @tap="handleLogin">
          <text class="btn-text">登录后申请</text>
        </view>
      </template>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { useUserStore } from "@/stores/user";
import { useGlobalStore } from "@/stores/global";
import { GigStatus, GigApplicationStatus } from "@/constants/gig";
import type { Gig, GigApplication } from "@/types/gig";
import { showActionConfirm } from "@/utils/ui/feedback";

interface Props {
  gig: Gig;
  userApplication?: GigApplication | null;
  applicationCount?: number;
  isPublisher?: boolean; // 新增：是否为发布者
  canApply?: boolean; // 新增：是否可以申请
}

const props = withDefaults(defineProps<Props>(), {
  userApplication: null,
  applicationCount: 0,
  isPublisher: false,
  canApply: false,
});

const emit = defineEmits<{
  apply: [];
  contact: [];
  share: [];
  withdraw: [];
  manageApplications: [];
  pauseResume: [];
  close: [];
  refresh: []; // 新增：刷新事件
}>();

const userStore = useUserStore();
const globalStore = useGlobalStore();

// 计算用户角色 - 优化逻辑
const userRole = computed(() => {
  if (!userStore.isLogin) return "guest";

  // 使用传入的 isPublisher 属性判断
  if (props.isPublisher) {
    return "recruiter";
  }

  return "seeker";
});

// 零工状态
const gigStatus = computed(() => props.gig.status);

// 用户申请状态
const applicationStatus = computed(() => props.userApplication?.status);

// 求职者按钮显示逻辑 - 优化
const showApplyButton = computed(() => {
  if (userRole.value !== "seeker") return false;
  if (props.isPublisher) return false; // 发布者不能申请自己的零工

  // 使用传入的 canApply 属性
  return props.canApply && gigStatus.value === GigStatus.Recruiting && !props.userApplication;
});

const showContactButton = computed(() => {
  if (userRole.value !== "seeker") return false;
  if (props.isPublisher) return false; // 发布者不需要联系自己

  // 用户申请已确认时显示联系按钮
  return applicationStatus.value === GigApplicationStatus.Confirmed;
});

const showWithdrawButton = computed(() => {
  if (userRole.value !== "seeker") return false;
  if (props.isPublisher) return false; // 发布者不能撤销申请

  // 用户申请待审核时显示撤销按钮
  return applicationStatus.value === GigApplicationStatus.Pending;
});

// 申请按钮文本和状态
const applyButtonText = computed(() => {
  if (
    props.gig.people_count &&
    props.gig.current_people_count >= props.gig.people_count
  ) {
    return "名额已满";
  }

  if (gigStatus.value === GigStatus.Paused) {
    return "暂停招聘";
  }

  return "立即申请";
});

const isApplyDisabled = computed(() => {
  return (
    !props.canApply ||
    gigStatus.value === GigStatus.Paused ||
    (props.gig.people_count &&
      props.gig.current_people_count >= props.gig.people_count)
  );
});

// 招聘者按钮显示逻辑 - 优化
const showManageButton = computed(() => {
  if (userRole.value !== "recruiter") return false;
  if (!props.isPublisher) return false; // 只有发布者才能管理

  // 招募中或进行中时显示管理按钮
  return [GigStatus.Recruiting, GigStatus.InProgress].includes(gigStatus.value);
});

const showPauseResumeButton = computed(() => {
  if (userRole.value !== "recruiter") return false;
  if (!props.isPublisher) return false; // 只有发布者才能暂停/恢复

  // 招募中可以暂停，暂停时可以恢复
  return [GigStatus.Recruiting, GigStatus.Paused].includes(gigStatus.value);
});

const showCloseButton = computed(() => {
  if (userRole.value !== "recruiter") return false;
  if (!props.isPublisher) return false; // 只有发布者才能结束

  // 招募中、暂停或进行中时可以结束
  return [
    GigStatus.Recruiting,
    GigStatus.Paused,
    GigStatus.InProgress,
  ].includes(gigStatus.value);
});

// 事件处理方法
const handleApply = async () => {
  if (isApplyDisabled.value) return;

  emit("apply");
};

const handleContact = () => {
  emit("contact");
};

const handleShare = () => {
  emit("share");
};

const handleWithdraw = async () => {
  await showActionConfirm(
    "撤销申请",
    "对此零工的申请",
    () => {
      emit("withdraw");
    }
  );
};

const handleManageApplications = () => {
  emit("manageApplications");
};

const showMoreActions = () => {
  const actions = [];

  // 根据零工状态添加相应操作
  if (showPauseResumeButton.value) {
    actions.push(
      gigStatus.value === GigStatus.Paused ? "恢复招聘" : "暂停招聘"
    );
  }

  if (showCloseButton.value) {
    actions.push("结束招聘");
  }

  if (actions.length === 0) {
    return;
  }

  uni.showActionSheet({
    itemList: actions,
    success: (res) => {
      const selectedAction = actions[res.tapIndex];

      if (selectedAction === "暂停招聘" || selectedAction === "恢复招聘") {
        handlePauseResume();
      } else if (selectedAction === "结束招聘") {
        handleClose();
      }
    },
  });
};

const handlePauseResume = async () => {
  const action = gigStatus.value === GigStatus.Paused ? "恢复" : "暂停";
  await showActionConfirm(
    `${action}招聘`,
    "此零工的招聘",
    () => {
      emit("pauseResume");
    }
  );
};

const handleClose = async () => {
  await showActionConfirm(
    "结束招聘",
    "此零工的招聘（结束后将无法继续接收申请）",
    () => {
      emit("close");
    }
  );
};

const handleLogin = () => {
  globalStore.showPrivacyPopup();
};
</script>

<style scoped lang="scss">
.actions-container {
  display: flex;
  align-items: center;
  padding: var(--spacing-12) var(--spacing-16);
  gap: var(--spacing-12);
  background-color: var(--bg-card);
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 120rpx;
  height: 80rpx;
  border-radius: var(--radius-base);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
  }
}

.share-btn {
  background-color: var(--bg-tag);
}

.action-icon {
  font-size: 32rpx;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-4);
}

.action-text {
  font-size: 22rpx;
  color: var(--text-secondary);
}

.main-actions {
  flex: 1;
  display: flex;
  gap: var(--spacing-12);
  align-items: center;
}

// 按钮样式
.primary-btn,
.secondary-btn,
.danger-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  border-radius: var(--radius-lg);
  font-weight: var(--font-weight-bold);
  position: relative;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
  }

  .btn-text {
    font-size: 28rpx;
    color: inherit;
  }

  .count-badge {
    position: absolute;
    top: -8rpx;
    right: -8rpx;
    min-width: 32rpx;
    height: 32rpx;
    line-height: 32rpx;
    text-align: center;
    background-color: var(--text-red);
    color: var(--text-inverse);
    font-size: 20rpx;
    border-radius: 16rpx;
    padding: 0 8rpx;
  }

  &.disabled {
    opacity: 0.6;
    background-color: var(--bg-tag) !important;
    color: var(--text-disable) !important;

    .btn-text {
      color: var(--text-disable);
    }

    &:active {
      transform: none;
    }
  }
}

.primary-btn {
  background: linear-gradient(135deg, var(--primary), #ff8533);
  color: var(--text-inverse);
  box-shadow: 0 8rpx 24rpx rgba(255, 109, 0, 0.3);

  &:active {
    box-shadow: 0 4rpx 12rpx rgba(255, 109, 0, 0.4);
  }
}

.secondary-btn {
  background-color: var(--bg-tag);
  color: var(--text-base);
  border: 1rpx solid var(--border-color);

  &:active {
    background-color: var(--primary);
    color: var(--text-inverse);
  }
}

.danger-btn {
  background-color: var(--bg-danger-light);
  color: var(--text-red);
  border: 1rpx solid var(--text-red);

  &:active {
    background-color: var(--text-red);
    color: var(--text-inverse);
  }
}

// 特定按钮样式调整
.apply-btn {
  min-width: 200rpx;
}

.manage-btn {
  min-width: 160rpx;
}

.more-actions-btn {
  min-width: 140rpx;
  position: relative;

  .more-icon {
    margin-left: var(--spacing-4);
    font-size: 20rpx;
    transition: transform 0.3s ease;
  }

  &:active .more-icon {
    transform: rotate(180deg);
  }
}

.login-btn {
  min-width: 200rpx;
}

// 响应式设计
@media (max-width: 750rpx) {
  .actions-container {
    gap: var(--spacing-8);
  }

  .action-item {
    width: 100rpx;
    height: 70rpx;
  }

  .action-icon {
    font-size: 28rpx;
  }

  .action-text {
    font-size: 20rpx;
  }

  .primary-btn,
  .secondary-btn,
  .danger-btn {
    height: 70rpx;

    .btn-text {
      font-size: 26rpx;
    }
  }
}
</style>
