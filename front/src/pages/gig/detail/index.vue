<template>
  <view class="container">
    <PageLayout
      :loading="isLoading"
      :error="fetchError"
      :isEmpty="!gigDetail"
      :onRetry="refreshGigDetail"
    >
      <!-- 页面内容 -->
      <scroll-view
        scroll-y
        enable-flex
        class="detail-scroll-view"
        @refresherrefresh="onPullDownRefresh"
        :refresher-triggered="isLoading"
      >
        <!-- 主要信息卡片 -->
        <view class="card main-info-card">
          <view class="gig-card__header">
            <view class="title-section">
              <text class="text-title">{{ gigDetail?.title }}</text>
              <view v-if="canApply" class="gig-status gig-status--recruiting">
                <text>可报名</text>
              </view>
            </view>
            <text class="gig-text-body-md gig-text-secondary description">{{
              gigDetail.description
            }}</text>
          </view>

          <view class="salary-section mb-3 mt-2">
            <AmountDisplay
              :amount="gigDetail.salary / 100"
              :unit="`元/${getSalaryUnitText(gigDetail.salary_unit)}`"
              color="primary"
              :bold="true"
            />
            <text class="gig-text-body-sm gig-text-secondary">{{
              formatDurationMinutes(gigDetail.work_duration)
            }}</text>
          </view>

          <view class="info-grid">
            <view class="info-item">
              <text class="i-solar:calendar-linear info-icon"></text>
              <text class="gig-text-body-md gig-text-primary">{{
                formatTimeRange(gigDetail.start_time, gigDetail.end_time)
              }}</text>
            </view>

            <view class="info-item">
              <text class="i-solar:map-point-linear info-icon"></text>
              <text class="text-secondary"
                >{{ gigDetail.address }}·{{ gigDetail.distance }}km</text
              >
            </view>
          </view>
        </view>

        <!-- 任务说明卡片 -->
        <view class="card task-card">
          <view class="gig-card__header">
            <text class="gig-text-title-sm gig-text-primary">任务说明</text>
          </view>
          <view class="task-content">
            <text class="text-secondary">{{ gigDetail.description }}</text>
          </view>
        </view>

        <!-- 工作要求卡片 -->
        <view class="card requirements-card">
          <view class="gig-card__header">
            <text class="gig-text-title-sm gig-text-primary">工作要求</text>
          </view>
          <view class="gig-card__body">
            <view class="requirement-item">
              <text class="requirement-icon">📍</text>
              <text class="gig-text-body-md gig-text-primary">GPS定位打卡</text>
            </view>
            <view class="requirement-item">
              <text class="requirement-icon">⏰</text>
              <text class="gig-text-body-md gig-text-primary"
                >24小时内核实，通过后发放薪酬</text
              >
            </view>
          </view>
        </view>

        <!-- 安全提醒卡片 -->
        <view class="card safety-card">
          <view class="gig-card__header safety-header">
            <text class="safety-icon">⚠️</text>
            <text class="gig-text-title-sm gig-text-warning">安全提醒</text>
          </view>
          <view class="gig-card__body safety-content">
            <view
              v-for="(tip, index) in safetyTips"
              :key="index"
              class="safety-item"
            >
              <view class="safety-number">
                <text class="gig-text-caption gig-text-warning">{{
                  index + 1
                }}</text>
              </view>
              <text class="gig-text-body-sm gig-text-secondary">{{ tip }}</text>
            </view>
          </view>
        </view>

        <!-- 发布者信息卡片 -->
        <view class="card publisher-card">
          <view class="gig-card__body publisher-info">
            <image
              :src="
                gigDetail.publisher?.avatar ||
                '/static/images/default-avatar.png'
              "
              class="publisher-avatar"
            />
            <view class="publisher-details">
              <view class="publisher-name-section">
                <text class="gig-text-title-sm gig-text-primary">{{
                  gigDetail.publisher?.nickname || "张先生"
                }}</text>
                <view class="gig-tag gig-tag--success">
                  <text>已认证雇主</text>
                </view>
              </view>
              <text class="gig-text-caption gig-text-tertiary"
                >发布零工146次</text
              >
            </view>
          </view>
        </view>
      </scroll-view>

      <!-- 底部操作栏 -->
      <BottomActionBar v-if="shouldShowBottomActions">
        <GigDetailActions
          :gig="gigDetail"
          :application-status="userApplication"
          :can-apply="canApply"
          :is-publisher="isPublisher"
          @apply="handleApply"
          @contact="handleContact"
          @share="handleShare"
          @withdraw="handleWithdraw"
          @manageApplications="handleManageApplications"
          @pauseResume="handlePauseResume"
          @close="handleClose"
          @refresh="refreshGigDetail"
        />
      </BottomActionBar>

      <!-- 申请零工模态框 -->
      <ApplyGigModal
        :show="showApplyModal"
        :gig="gigDetail"
        @close="showApplyModal = false"
        @submit="handleApplySubmit"
      />
    </PageLayout>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { onLoad, onPullDownRefresh } from "@dcloudio/uni-app";
import { useGigDetail } from "@/services/gig";
import { useGigApplication, checkAppStatus } from "@/services/gigApplication";
import { useUserStore } from "@/stores";
import { formatGigSalary, getSalaryUnitText } from "@/utils/business/gig";
import { formatTimeRange } from "@/utils/core/date";
import { navigateTo, showToast, formatDurationMinutes } from "@/utils";
import PageLayout from "@/components/PageLayout.vue";
import AmountDisplay from "@/components/common/AmountDisplay.vue";
import BottomActionBar from "@/components/common/BottomActionBar.vue";
import GigDetailActions from "./components/GigDetailActions.vue";
import ApplyGigModal from "./components/ApplyGigModal.vue";

const userStore = useUserStore();

// 响应式地存储从 URL 获取的 gigId
const gigId = ref<number>(0);

// =================================================================
// 1. 调用核心业务 Hook，传入 gigId
//    所有数据 (gigDetail)、状态 (isLoading) 和业务判断 (canApply) 都从此获取
// =================================================================
const { gigDetail, isLoading, fetchError, canApply, refreshGigDetail } =
  useGigDetail(gigId);

// =================================================================
// 2. 申请相关状态和逻辑 - 只有非发布者才需要检查申请状态
// =================================================================
const { applyForGig } = useGigApplication();

// 判断当前用户是否为发布者
const isPublisher = computed(() => {
  return gigDetail.value && userStore.user && gigDetail.value.user_id === userStore.user.id;
});

// 申请状态相关
const userApplication = ref(null);
const isLoadingAppStatus = ref(false);

// 检查申请状态的函数
const checkApplicationStatus = async () => {
  if (!userStore.isLogin || isPublisher.value || gigId.value <= 0) {
    userApplication.value = null;
    return;
  }

  try {
    isLoadingAppStatus.value = true;
    const { application, checkStatus } = checkAppStatus(gigId.value);
    await checkStatus();
    userApplication.value = application.value;
  } catch (error) {
    console.error('检查申请状态失败:', error);
    userApplication.value = null;
  } finally {
    isLoadingAppStatus.value = false;
  }
};

// 监听相关状态变化，动态检查申请状态
watch([() => userStore.isLogin, isPublisher, gigId], () => {
  checkApplicationStatus();
}, { immediate: true });

// 页面状态
const showApplyModal = ref(false);
const applicationCount = ref(0);

// 计算是否显示底部操作栏
const shouldShowBottomActions = computed(() => {
  // 必须有零工详情数据
  if (!gigDetail.value) return false;
  
  // 如果用户未登录，显示登录提示按钮
  if (!userStore.isLogin) return true;
  
  // 如果是发布者，显示管理相关按钮
  if (isPublisher.value) return true;
  
  // 如果是求职者，显示申请相关按钮
  return true;
});

// =================================================================
// 3. 页面数据
// =================================================================

// 任务说明列表
const taskList = computed(() => {
  // 默认任务列表，基于设计图内容
  return [
    "主要负责办公室日常保洁工作，包括地面清洁、办公桌椅整理",
    "保持办公区域干净整洁，定期清洁卫生间",
    "整理会议室，会议及时清理并保持整洁环境",
    "需要自备基本清洁工具，清洁内容用品方提供",
    "工作认真负责，细心耐心，有良好的服务意识",
  ];
});

// 安全提醒列表
const safetyTips = ref([
  "首次见面请选择公共场所，避免偏僻地点",
  "收到转账要求请务必核实对方身份",
  "遇到异常情况，请立即联系平台客服",
]);

// =================================================================
// 4. 生命周期钩子
// =================================================================
onLoad((options) => {
  if (options && options.id) {
    const newGigId = parseInt(options.id, 10);
    // 清除之前的数据，避免缓存问题
    if (gigId.value !== newGigId) {
      gigId.value = newGigId;
      // 强制刷新数据
      setTimeout(() => {
        refreshGigDetail();
      }, 100);
    }
  } else {
    showToast("无效的零工ID");
  }
});

// 下拉刷新逻辑
onPullDownRefresh(async () => {
  await refreshGigDetail(); // 调用 hook 返回的刷新函数
  // 如果需要检查申请状态，也一并刷新
  await checkApplicationStatus();
  uni.stopPullDownRefresh();
});

// =================================================================
// 5. 页面方法
// =================================================================

/**
 * 返回上一页
 */
function goBack() {
  uni.navigateBack();
}

/**
 * 分享功能
 */
function handleShare() {
  uni.showShareMenu({
    withShareTicket: true,
    menus: ["shareAppMessage", "shareTimeline"],
  });
}

/**
 * 举报功能
 */
function handleReport() {
  showToast("报名成功后可见");
}

// =================================================================
// 4. 页面方法 (Methods)
//    这些方法现在非常简洁，只负责用户交互和调用 Hook 返回的函数
// =================================================================

// =================================================================
// 新的事件处理方法
// =================================================================

/**
 * 处理申请按钮点击
 */
function handleApply() {
  if (!userStore.isLogin) {
    showToast("请先登录");
    return;
  }
  showApplyModal.value = true;
}

/**
 * 处理申请表单提交
 */
async function handleApplySubmit(formData: any) {
  try {
    // 构造符合后端API的数据结构
    const apiData = {
      gig_id: gigId.value,
      contact_name: formData.contactName,
      contact_phone: formData.contactPhone,
      has_experience: formData.hasExperience,
      experience_description: formData.remarks || '', // 备注作为经验描述
      message: formData.remarks || '', // 同时作为留言
    };

    await applyForGig(apiData);

    showApplyModal.value = false;
    // 刷新申请状态和零工详情
    await checkApplicationStatus();
    await refreshGigDetail();
  } catch (error) {
    console.error("申请提交失败:", error);
  }
}

/**
 * 联系雇主
 */
function handleContact() {
  const phone = gigDetail.value?.contact_phone;
  if (!phone) {
    showToast("暂无联系方式");
    return;
  }
  uni.makePhoneCall({ phoneNumber: phone });
}

/**
 * 撤销申请
 */
async function handleWithdraw() {
  // TODO: 实现撤销申请逻辑
  showToast("撤销申请功能待实现");
}

/**
 * 管理申请
 */
function handleManageApplications() {
  navigateTo(`/pages/gig/applicants?gigId=${gigId.value}`);
}

/**
 * 暂停/恢复招聘
 */
async function handlePauseResume() {
  // TODO: 实现暂停/恢复招聘逻辑
  showToast("暂停/恢复招聘功能待实现");
}

/**
 * 结束招聘
 */
async function handleClose() {
  // TODO: 实现结束招聘逻辑
  showToast("结束招聘功能待实现");
}
</script>

<style lang="scss" scoped>
@import "@/styles/gig-common.scss";

/* ==================== 滚动容器 ==================== */
.detail-scroll-view {
  flex: 1;
  padding: var(--spacing-12);
  /* 增加底部内边距，以避免内容被固定的操作栏遮挡 */
  padding-bottom: 180rpx;
  box-sizing: border-box;
}

/* ==================== 主要信息卡片 ==================== */
.main-info-card {
  margin-bottom: var(--spacing-12);

  .title-section {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-8);
    gap: var(--spacing-8);
  }

  .description {
    line-height: var(--line-height-loose);
  }

  .salary-section {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    gap: var(--spacing-4);
    background: var(--primary-50);
    padding: 24rpx;
    border-radius: 12rpx;
  }

  .info-grid {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-6);
  }

  .info-item {
    display: flex;
    align-items: center;
    gap: 8rpx;
    color: var(--text-secondary);
  }

  .info-icon {
    font-size: var(--font-size-lg);
    width: var(--font-size-lg);
    text-align: center;
    flex-shrink: 0;
  }
}

/* ==================== 工作要求卡片 ==================== */
.requirements-card {
  margin-bottom: var(--spacing-12);

  .requirement-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-8);
    margin-bottom: var(--spacing-8);

    &:last-child {
      margin-bottom: 0;
    }
  }

  .requirement-icon {
    font-size: var(--font-size-lg);
    width: var(--font-size-lg);
    text-align: center;
    flex-shrink: 0;
  }
}

/* ==================== 安全提醒卡片 ==================== */
.safety-card {
  background: var(--bg-warning-light);
  border: 1rpx solid var(--text-yellow);
  margin-bottom: var(--spacing-12);

  .safety-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-8);
  }

  .safety-icon {
    font-size: var(--font-size-lg);
  }

  .safety-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-8);
  }

  .safety-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-8);
  }

  .safety-number {
    width: 24rpx;
    height: 24rpx;
    border-radius: 50%;
    background: var(--text-yellow);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    margin-top: 2rpx;

    text {
      color: var(--text-inverse);
      font-size: 20rpx;
      font-weight: var(--font-weight-bold);
    }
  }
}

/* ==================== 发布者信息卡片 ==================== */
.publisher-card {
  margin-bottom: var(--spacing-12);

  .publisher-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-12);
  }

  .publisher-avatar {
    width: 96rpx;
    height: 96rpx;
    border-radius: 50%;
    background: var(--bg-tag);
    flex-shrink: 0;
  }

  .publisher-details {
    flex: 1;
  }

  .publisher-name-section {
    display: flex;
    align-items: center;
    gap: var(--spacing-8);
    margin-bottom: var(--spacing-4);
  }
}
</style>
