<template>
  <PageLayout :show-nav-bar="false" bg-color="transparent">
    <view class="container">
      <uni-nav-bar
        :border="false"
        fixed
        background-color="transparent"
        :status-bar="true"
      ></uni-nav-bar>
      <!-- 顶部定位和搜索栏 -->
      <view class="header flex items-center justify-between px-30rpx py-16rpx">
        <view class="location flex items-center" @tap="openCityPicker">
          <text class="text-32rpx font-medium mr-8rpx">{{ currentCity }}</text>
          <text class="i-carbon-chevron-down text-24rpx text-info"></text>
        </view>

        <view
          class="search-box w-[65%] flex items-center bg-search rounded-full px-30rpx py-16rpx"
          @tap="toSearch"
        >
          <text class="i-carbon-search mr-10rpx text-grey"></text>
          <text class="text-grey text-28rpx">{{ searchPlaceholder }}</text>
        </view>

        <view class="ml-20rpx relative">
          <text class="i-carbon-notification text-40rpx"></text>
          <view v-if="hasNotification" class="notification-dot"></view>
        </view>
      </view>

      <!-- 核心功能导航区 -->
      <view
        class="core-nav py-30rpx px-20rpx mb-20rpx rounded-lg mx-16rpx mt-16rpx"
      >
        <view class="flex justify-between">
          <view
            v-for="(item, index) in coreNavItems"
            :key="index"
            class="nav-item flex flex-col items-center"
            @tap="navigateTo(item.linkPath)"
          >
            <view class="icon-container">
              <image :src="item.path" mode="widthFix" />
            </view>
            <text class="text-28rpx">{{ item.title }}</text>
          </view>
        </view>
      </view>

      <!-- 二级服务宫格 -->
      <view class="services-container">
        <view class="services-grid">
          <view
            v-for="(service, index) in services"
            :key="index"
            class="service-item flex flex-col gap-8rpx items-center"
            @tap="navigateTo(service.path)"
          >
            <!-- 使用 SVG 图片 -->
            <view
              class="service-icon-container"
              :style="{ color: service.color }"
            >
              <image :src="service.imagePath" mode="aspectFill" />
            </view>
            <text class="text-26rpx text-center line-clamp-1">{{
              service.name
            }}</text>
          </view>
        </view>
      </view>

      <!-- 动态推荐内容 -->
      <RecommendContent />

      <!-- 发布悬浮按钮 -->
      <view class="publish-fab" @tap="navigateToPublishPage">
        <text class="i-carbon-add text-66rpx text-white"></text>
      </view>

      <!-- 开发快捷登录 -->
      <DevQuickLogin />
    </view>
  </PageLayout>
</template>

<script setup lang="ts">
import { ref } from "vue";
import PageLayout from "@/components/PageLayout.vue";
import { assetsSvg } from "@/utils/assetsUtil";
import { useJobStore } from "@/stores";
import RecommendContent from "@/components/home/<USER>";
import DevQuickLogin from "@/components/home/<USER>";

const jobStore = useJobStore();
const userRole = jobStore.currentRole || "jobseeker";

// 城市定位
const currentCity = ref("北京");
const searchPlaceholder = ref("搜索商家");

// 是否有未读通知
const hasNotification = ref(true);

// 核心导航项
const coreNavItems = [
  {
    title: "求职招聘",
    icon: "i-carbon-user-profile",
    path: assetsSvg.recruiting,
    linkPath:
      userRole === "jobseeker"
        ? "/pages/job/jobseeker/index"
        : "/pages/job/recruiter/index",
  },
  {
    title: "二手房",
    icon: "i-carbon-building",
    path: assetsSvg.secondHandHouse,
    linkPath: "/pages/house/secondHouse/index",
  },
  {
    title: "租房",
    icon: "i-carbon-home",
    path: assetsSvg.renting,
    linkPath: "/pages/house/rent/index",
  },
  {
    title: "新房",
    icon: "i-carbon-apartment",
    path: assetsSvg.newHouse,
    linkPath: "/pages/house/newHouse/index",
  },
  {
    title: "交友",
    icon: "i-carbon-friendship",
    path: assetsSvg.dating,
    linkPath: "/pages/dating/index",
  },
];

// 服务分类十宫格数据
const services = [
  {
    name: "零工",
    imagePath: assetsSvg.gigJobs,
    color: "#ff6d00", // 橙色
    path: "/pages/gig/index",
  },
  {
    name: "管道疏通",
    imagePath: assetsSvg.plumbing,
    color: "#2196f3", // 蓝色
    path: "/pages/service/plumbing",
  },
  {
    name: "家居装修",
    imagePath: assetsSvg.homeDecoration,
    color: "#4caf50", // 绿色
    path: "/pages/service/decoration",
  },
  {
    name: "家政保洁",
    imagePath: assetsSvg.cleaning,
    color: "#9c27b0", // 紫色
    path: "/pages/service/clean",
  },
  {
    name: "搬家拉货",
    imagePath: assetsSvg.moving,
    color: "#ff9800", // 深橙色
    path: "/pages/service/moving",
  },
  {
    name: "保姆月嫂",
    imagePath: assetsSvg.nannyMaternity,
    color: "#e91e63", // 粉红色
    path: "/pages/service/nanny",
  },
  {
    name: "家电维修",
    imagePath: assetsSvg.applianceRepair,
    color: "#607d8b", // 蓝灰色
    path: "/pages/service/appliance",
  },
  {
    name: "开锁换锁",
    imagePath: assetsSvg.locksmith,
    color: "#795548", // 棕色
    path: "/pages/service/locksmith",
  },
  {
    name: "二手/回收",
    imagePath: assetsSvg.recycle,
    color: "#00bcd4", // 青色
    path: "/pages/service/recycle",
  },
  {
    name: "结婚摄影",
    imagePath: assetsSvg.weddingPhotography,
    color: "#f44336", // 红色
    path: "/pages/service/wedding",
  },
];

// 打开城市选择器
const openCityPicker = () => {
  uni.showToast({
    title: "城市选择功能开发中",
    icon: "none",
  });
};

// 跳转到搜索页面
const toSearch = () => {
  uni.showToast({
    title: "搜索功能开发中",
    icon: "none",
  });
};

// 导航到指定页面
const navigateTo = (path: string) => {
  if (!path) return;
  uni.navigateTo({
    url: path,
  });
};

// 跳转到发布页面
const navigateToPublishPage = () => {
  uni.navigateTo({
    url: "/pages/publish/index",
    animationType: "slide-in-bottom",
    animationDuration: 300,
  });
};
</script>

<style scoped>
.container {
  background: radial-gradient(
      ellipse at top center,
      rgba(255, 166, 50, 0.15) 0%,
      rgba(255, 134, 10, 0.2) 10%,
      rgba(243, 244, 249, 0) 50%
    ),
    var(--bg-page);
  background-size: 120% 60vh;
  background-position: top center;
  background-repeat: no-repeat;
  min-height: 100vh;
}

.header {
  background: transparent;
}

.core-nav {
  background: transparent;
}

.services-container {
  background: transparent;
  margin: 0 20rpx 28rpx 20rpx;
}

.location {
  max-width: 20%;
}

/* 核心导航区 */
.icon-container {
  width: 92rpx;
  height: 92rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bg-gradient-0 {
  background: linear-gradient(135deg, #ff9a9e, #ff6b6b);
}

.bg-gradient-1 {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.bg-gradient-2 {
  background: linear-gradient(135deg, #43e97b, #38f9d7);
}

.bg-gradient-3 {
  background: linear-gradient(135deg, #fa709a, #fee140);
}

.bg-gradient-4 {
  background: linear-gradient(135deg, #a18cd1, #fbc2eb);
}

/* 服务分类宫格布局 */
.services-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 30rpx 20rpx;
  padding: 10rpx 0;
}

.service-item {
  position: relative;
  transition: transform 0.2s;
}

.service-item:active {
  transform: scale(0.95);
}

.service-icon-container {
  width: 56rpx;
  height: 56rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-dot {
  position: absolute;
  top: -6rpx;
  right: -6rpx;
  width: 16rpx;
  height: 16rpx;
  background-color: var(--primary);
  border-radius: 50%;
}

.text-primary {
  color: var(--primary);
}

/* 发布悬浮按钮 */
.publish-fab {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  position: fixed;
  bottom: 140rpx;
  right: 32rpx;
  z-index: 99999;
  pointer-events: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ffe53b;
  background-image: linear-gradient(147deg, #ffe53b 0%, #ff2525 74%);
  box-shadow: 0px 20rpx 20rpx rgba(0, 0, 0, 0.151);
}

/* .publish-fab {
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(135deg, #ff6d00, #ff9500);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(255, 109, 0, 0.3);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
} */

.publish-fab :active {
  transform: scale(0.9);
  box-shadow: 0 4rpx 12rpx rgba(255, 109, 0, 0.4);
}
</style>
