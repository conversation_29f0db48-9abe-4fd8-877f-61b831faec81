<template>
  <view class="home-container flex flex-col h-screen bg-gray-100">
    <!-- 顶部搜索栏 -->
    <view
      class="header flex items-center justify-between px-30rpx py-16rpx bg-white sticky top-0 z-10 shadow-sm"
    >
      <view
        class="flex-1 flex items-center bg-gray-100 rounded-full px-30rpx py-16rpx mr-20rpx"
      >
        <text class="i-carbon-search mr-10rpx color-gray-500"></text>
        <text class="color-gray-500 text-28rpx">搜索商家、服务</text>
      </view>
      <view class="relative">
        <text class="i-carbon-notification text-40rpx color-gray-700"></text>
        <view v-if="hasNotification" class="notification-dot"></view>
      </view>
    </view>

    <!-- 核心功能入口 -->
    <view class="core-features bg-white px-20rpx py-30rpx flex justify-around items-center">
      <view
        v-for="feature in topFeatures"
        :key="feature.name"
        class="feature-item flex flex-col items-center w-1/5"
        @click="navigateTo(feature.path)"
      >
        <view
          class="icon-wrapper w-80rpx h-80rpx rounded-full flex items-center justify-center mb-10rpx"
          :class="feature.bgColor"
        >
          <text :class="[feature.icon, feature.color]" class="text-44rpx"></text>
        </view>
        <text class="text-24rpx color-gray-700">{{ feature.name }}</text>
      </view>
    </view>

    <!-- 分隔线 -->
    <view class="h-16rpx bg-gray-100"></view>

    <!-- 九宫格快捷入口 -->
    <view class="grid-menu bg-white px-10rpx py-20rpx">
      <scroll-view scroll-x class="whitespace-nowrap">
        <view class="flex flex-col flex-wrap h-[200rpx] content-start">
          <!-- 假设每个item高度约100rpx (图标+文字+padding), 两行即200rpx -->
          <view
            v-for="item in gridMenuItems"
            :key="item.name"
            class="grid-item w-[150rpx] h-[100rpx] flex flex-col items-center justify-center"
            @click="navigateTo(item.path)"
          >
            <text :class="item.icon" class="text-48rpx mb-8rpx" :style="{ color: item.color || '#333' }"></text>
            <text class="text-22rpx color-gray-600">{{ item.name }}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 内容区域 -->
    <scroll-view scroll-y class="flex-1">
      <recommend-content :show-banner="showBanner"></recommend-content>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
// import tuiTab from "@/components/thorui/tui-tab/tui-tab.vue"; // No longer needed
import RecommendContent from "@/components/home/<USER>";
// import JobContent from "@/components/home/<USER>"; // No longer directly used here
// import DatingContent from "@/components/home/<USER>"; // No longer directly used here
// import HouseContent from "@/components/home/<USER>"; // No longer directly used here

// 是否有未读通知
const hasNotification = ref(true);

// 是否显示banner广告 (给RecommendContent)
const showBanner = ref(true);

// 主题色 (备用，UnoCSS可以直接使用主题色)
// const primaryColor = ref("#ff6d00");

const topFeatures = ref([
  { name: '找工作', icon: 'i-carbon-briefcase', path: '/pages/job/index', color: 'text-orange-500', bgColor: 'bg-orange-100' },
  { name: '二手房', icon: 'i-carbon-home', path: '/pages/house/secondHouse/index', color: 'text-blue-500', bgColor: 'bg-blue-100' },
  { name: '租房', icon: 'i-carbon-building-insights-2', path: '/pages/house/rent/index', color: 'text-green-500', bgColor: 'bg-green-100' },
  { name: '本地服务', icon: 'i-carbon-settings-services', path: '/pages/services/index', color: 'text-purple-500', bgColor: 'bg-purple-100' },
  { name: '二手车', icon: 'i-carbon-car', path: '/pages/vehicle/index', color: 'text-red-500', bgColor: 'bg-red-100' },
]);

const gridMenuItems = ref([
  { name: '附近工作', icon: 'i-carbon-location-person', path: '/pages/job/nearby', color: '#FF6B6B' },
  { name: '维修服务', icon: 'i-carbon-tool-kit', path: '/pages/services/repair', color: '#4ECDC4' },
  { name: '商铺写字楼', icon: 'i-carbon-store', path: '/pages/house/commercial', color: '#45B7D1' },
  { name: '家庭保洁', icon: 'i-carbon-clean', path: '/pages/services/cleaning', color: '#FFA07A' },
  { name: '新房', icon: 'i-carbon-real-estate', path: '/pages/house/new', color: '#FFD700' },
  { name: '家电维修', icon: 'i-carbon-chip', path: '/pages/services/appliance', color: '#98D8C8' },
  { name: '附近钟点工', icon: 'i-carbon-alarm', path: '/pages/services/hourly', color: '#FF7F50' },
  { name: '保姆月嫂', icon: 'i-carbon-pedestrian-child', path: '/pages/services/nanny', color: '#6A5ACD' },
  { name: '新车', icon: 'i-carbon-car-front', path: '/pages/vehicle/new', color: '#20B2AA' },
  { name: '做饭保洁', icon: 'i-carbon-restaurant', path: '/pages/services/cooking', color: '#FF8C00' },
]);


const navigateTo = (path: string) => {
  if (!path || path === '/pages/services/index' || path === '/pages/vehicle/index' || path.includes('nearby') || path.includes('commercial') || path.includes('new') || path.includes('appliance') || path.includes('hourly') || path.includes('nanny') || path.includes('cooking')) {
    // 对于尚未创建或通用的路径，可以先提示
    uni.showToast({
      title: '功能建设中',
      icon: 'none',
    });
    console.log(`Navigate to: ${path}`); // 打印路径用于调试
    return;
  }
  uni.navigateTo({ url: path });
};

// Removed old tab and swiper logic:
// const tabs = [...]
// const activeTabIndex = ref(0);
// const handleTabChange = (e: any) => { ... };
// const handleSwiperChange = (e: any) => { ... };
</script>

<style lang="scss" scoped>
.home-container {
  // background-color: #f5f5f5; // 全局背景色已在 template 中通过unoCSS设置
}

.header {
  // UnoCSS classes used directly
}

.notification-dot {
  position: absolute;
  top: -6rpx;
  right: -6rpx;
  width: 16rpx;
  height: 16rpx;
  background-color: $primary; // $primary comes from uni.scss
  border-radius: 50%;
}

.core-features {
  // UnoCSS classes used directly
  .feature-item {
    // UnoCSS classes used directly
    .icon-wrapper {
      // UnoCSS classes used directly
    }
  }
}

.grid-menu {
  // UnoCSS classes used directly
  .grid-item {
    // UnoCSS classes used directly
    // Forcing width for 5 items per effective row in the scroll view
    // width: 20%; // This is tricky with flex-col flex-wrap, better to use fixed width like w-[150rpx]
  }
  // Hide scrollbar for a cleaner look if desired (platform-dependent)
  // Might need ::-webkit-scrollbar { display: none; } for some platforms
  scroll-view ::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Edge */
    width: 0 !important;
    height: 0 !important;
    -webkit-appearance: none;
    background: transparent;
  }
}

// Content area styling will be handled by RecommendContent and its scroll-view
</style>
