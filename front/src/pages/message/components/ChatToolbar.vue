<template>
  <view class="chat-toolbar">
    <view class="chat-input-bar">
      <!-- 语音和文本输入切换 -->
      <view class="input-mode" @click="toggleInputMode">
        <tui-icon
          :name="isVoiceMode ? 'imkeyboard' : 'imvoice'"
          size="52"
          unit="rpx"
          color="#666"
        />
      </view>

      <!-- 文本输入框 -->
      <view
        v-if="!isVoiceMode"
        class="input-container"
        :class="{ expanded: isTextareaExpanded }"
      >
        <textarea
          class="input-textarea"
          v-model="inputText"
          :adjust-position="false"
          :auto-height="true"
          :show-confirm-bar="false"
          confirm-type="send"
          :confirm-hold="true"
          :auto-blur="false"
          :cursor-spacing="10"
          :cursor="cursorPosition.start"
          :selection-start="cursorPosition.start"
          :selection-end="cursorPosition.end"
          :maxlength="1000"
          :focus="textareaFocus"
          :hold-keyboard="true"
          @focus="onInputFocus"
          @blur="onInputBlur"
          @confirm="sendMessage"
          @input="onTextareaInput"
          @selection-change="onSelectionChange"
        />
      </view>

      <!-- 语音输入按钮 -->
      <view v-else class="voice-container">
        <voice-recorder
          @start="onVoiceStart"
          @finish="onVoiceFinish"
          @cancel="onVoiceCancel"
          @update-status="onVoiceStatusUpdate"
        />
      </view>

      <!-- 表情按钮 -->
      <view class="emoji-button" @click="toggleEmojiPanel">
        <tui-icon
          :name="showEmojiPanel ? 'imkeyboard' : 'imface'"
          size="52"
          unit="rpx"
          color="#666"
        ></tui-icon>
      </view>

      <!-- 更多功能按钮 -->
      <view class="more-button" @click="toggleMorePanel">
        <tui-icon name="add" size="56" unit="rpx" color="#666"></tui-icon>
        <!-- <uni-icons :type="showMorePanel ? 'closeempty' : 'plusempty'" size="24" color="#666" /> -->
      </view>
    </view>

    <!-- Emoji面板 -->
    <view
      class="emoji-panel-container"
      :class="{ 'panel-show': showEmojiPanel }"
      :style="[{ height: showEmojiPanel ? '480rpx' : '0px' }]"
    >
      <scroll-view scroll-y class="flex-1">
        <view class="emoji-panel">
          <view
            v-for="(emoji, index) in emojiCodes"
            :key="index"
            class="emoji-item"
            @click.stop="insertEmoji(emoji)"
          >
            {{ emoji }}
          </view>
        </view>
      </scroll-view>
      <view class="emoji-footer">
        <view class="emoji-button-group">
          <view
            class="emoji-delete-button"
            :class="{ disabled: !inputText.trim() }"
            @click="inputText.trim() && deleteLastCharacter()"
          >
            <tui-icon
              name="backspace"
              :size="44"
              unit="rpx"
              color="#333"
            ></tui-icon>
          </view>
          <view class="flex-1"></view>
          <view
            class="emoji-send-button"
            :class="{ disabled: !inputText.trim() }"
            @click="inputText.trim() && sendMessage()"
          >
            发送
          </view>
        </view>
      </view>
    </view>

    <!-- 更多功能面板 -->
    <view
      class="more-panel-container"
      :class="{ 'panel-show': showMorePanel }"
      :style="[{ height: showMorePanel ? '350rpx' : '0px' }]"
    >
      <view class="more-grid">
        <view class="more-item" @click="onMoreItemClick('album')">
          <view class="more-icon-wrapper">
            <uni-icons type="image" size="32" color="#333" />
          </view>
          <text>相册</text>
        </view>
        <view class="more-item" @click="onMoreItemClick('camera')">
          <view class="more-icon-wrapper">
            <uni-icons type="camera" size="32" color="#333" />
          </view>
          <text>拍照</text>
        </view>
        <view class="more-item" @click="onMoreItemClick('location')">
          <view class="more-icon-wrapper">
            <uni-icons type="location" size="32" color="#333" />
          </view>
          <text>位置</text>
        </view>
        <!-- <view class="more-item" @click="onMoreItemClick('call')">
          <view class="more-icon-wrapper">
            <uni-icons type="phone" size="28" color="#333" />
          </view>
          <text>语音通话</text>
        </view> -->
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import tuiIcon from "@/components/thorui/tui-icon/tui-icon.vue";
import VoiceRecorder from "./VoiceRecorder.vue";
import { emojiCodes } from "@/constants/static/emoji";

// 组件事件
const emit = defineEmits<{
  (e: "sendText", text: string): void;
  (e: "sendVoice", duration: number): void;
  (e: "toggleEmojiPanel", show: boolean): void;
  (e: "toggleMorePanel", show: boolean): void;
  (e: "startRecording"): void;
  (e: "endRecording", duration: number): void;
  (e: "updateRecordingStatus", isCanceled: boolean): void;
  (e: "moreItemClick", type: string): void;
}>();

// 输入模式
const isVoiceMode = ref(false);
const inputText = ref("");
const showEmojiPanel = ref(false);
const showMorePanel = ref(false);
const keyboardHeight = ref(0);
const textareaHeight = ref(36); // 默认高度36px (72rpx)
const textareaFocus = ref(false);
const isKeyboardShowing = ref(false);
const isTextareaExpanded = ref(false); // 输入框是否扩展

// 记录光标位置
const cursorPosition = ref({
  start: 0,
  end: 0,
});

// 保存最后有效的光标位置
const lastValidCursorPosition = ref({
  start: 0,
  end: 0,
});

/**
 * 处理选择变化事件，记录光标位置
 * @param e 选择事件对象
 */
const onSelectionChange = (e: any) => {
  if (e.detail && typeof e.detail.cursor !== "undefined") {
    cursorPosition.value.start = e.detail.cursor;
    cursorPosition.value.end = e.detail.cursor;
  } else if (
    e.detail &&
    e.detail.start !== undefined &&
    e.detail.end !== undefined
  ) {
    cursorPosition.value.start = e.detail.start;
    cursorPosition.value.end = e.detail.end;
  }

  // 实时保存有效的光标位置
  if (textareaFocus.value) {
    saveCursorPosition();
  }
};

/**
 * 更新键盘高度变化
 * 当键盘高度发生变化时调用
 * @param {Object} res - 键盘高度信息
 */
const updateKeyboardHeightChange = (res: any) => {
  const height = typeof res === "number" ? res : res?.height || 0;

  // 记录键盘状态
  isKeyboardShowing.value = height > 0;

  // 如果键盘弹出，关闭表情和更多面板
  if (height > 0) {
    if (showEmojiPanel.value) {
      showEmojiPanel.value = false;
      emit("toggleEmojiPanel", false);
    }

    if (showMorePanel.value) {
      showMorePanel.value = false;
      emit("toggleMorePanel", false);
    }

    // 切换到文本输入模式
    if (isVoiceMode.value) {
      isVoiceMode.value = false;
    }
  }

  keyboardHeight.value = height;
};

/**
 * 处理键盘隐藏事件
 * 当用户尝试隐藏键盘时调用，处理表情面板的状态
 */
const hidedKeyboard = () => {
  isKeyboardShowing.value = false;
  keyboardHeight.value = 0;

  // 在z-paging聊天记录模式下，当键盘隐藏时保存光标位置
  // 这样在下次聚焦时可以恢复到正确位置
  if (textareaFocus.value) {
    saveCursorPosition();
  }
};

// 输入框聚焦
const onInputFocus = () => {
  textareaFocus.value = true;
  hideAllPanels();

  // 如果有文本内容且当前光标位置可能被重置，则恢复保存的位置
  if (inputText.value.length > 0) {
    const textLength = inputText.value.length;

    // 如果当前光标位置为0，但有保存的有效光标位置，则恢复
    if (cursorPosition.value.start === 0 && cursorPosition.value.end === 0) {
      const savedStart = lastValidCursorPosition.value.start;
      const savedEnd = lastValidCursorPosition.value.end;

      if (
        savedStart >= 0 &&
        savedStart <= textLength &&
        savedEnd >= 0 &&
        savedEnd <= textLength
      ) {
        nextTick(() => {
          cursorPosition.value = { start: savedStart, end: savedEnd };
        });
      } else if (savedStart > 0 || savedEnd > 0) {
        // 如果保存的位置超出范围，设置到文本末尾
        nextTick(() => {
          cursorPosition.value = { start: textLength, end: textLength };
        });
      }
    }
  }
};

// 输入框失去焦点
const onInputBlur = () => {
  // 当输入框失去焦点时，保存当前的光标位置
  saveCursorPosition();
};

// 处理文本输入
const onTextareaInput = (e: any) => {
  // 文本改变时，确保光标位置的有效性
  const text = e.detail.value;
  if (cursorPosition.value.start > text.length) {
    cursorPosition.value.start = text.length;
  }
  if (cursorPosition.value.end > text.length) {
    cursorPosition.value.end = text.length;
  }

  // 根据文本内容判断是否需要扩展输入框
  checkTextareaExpansion(text);
};

// 检查输入框是否需要扩展
const checkTextareaExpansion = (text: string) => {
  // 如果文本为空，收起输入框
  if (!text.trim()) {
    isTextareaExpanded.value = false;
    return;
  }

  // 简单判断：如果包含换行符，则扩展
  const hasLineBreak = text.includes("\n");

  isTextareaExpanded.value = hasLineBreak;
};

// 智能保存光标位置
const saveCursorPosition = () => {
  if (inputText.value.length > 0) {
    const textLength = inputText.value.length;
    const validStart = Math.min(
      Math.max(0, cursorPosition.value.start),
      textLength
    );
    const validEnd = Math.min(
      Math.max(0, cursorPosition.value.end),
      textLength
    );
    lastValidCursorPosition.value = { start: validStart, end: validEnd };
  } else {
    lastValidCursorPosition.value = { start: 0, end: 0 };
  }
};

// 切换输入模式
const toggleInputMode = () => {
  // 先隐藏所有面板（表情面板和更多面板）
  hideAllPanels();

  // 如果是文字模式，则切换到语音模式
  if (textareaFocus.value) {
    textareaFocus.value = false;
    uni.hideKeyboard();
  }

  isVoiceMode.value = !isVoiceMode.value;

  // 添加震动反馈
  // uni.vibrateShort()

  // 切换到文本模式时
  if (!isVoiceMode.value) {
    // 聚焦输入框并显示键盘
    nextTick(() => {
      textareaFocus.value = true;
    });
  } else {
    // 切换到语音模式时，隐藏键盘并重置输入框状态
    uni.hideKeyboard();
    isTextareaExpanded.value = false;
  }
};

// 隐藏所有面板
const hideAllPanels = () => {
  if (showEmojiPanel.value) {
    showEmojiPanel.value = false;
    emit("toggleEmojiPanel", false);
  }

  if (showMorePanel.value) {
    showMorePanel.value = false;
    emit("toggleMorePanel", false);
  }
};

// 切换表情面板
const toggleEmojiPanel = () => {
  const wasShowing = showEmojiPanel.value;

  // 先隐藏所有面板
  hideAllPanels();

  // 如果不是关闭操作，打开表情面板
  if (!wasShowing) {
    // 在打开表情面板前，保存当前有效的光标位置
    if (textareaFocus.value || inputText.value.length > 0) {
      saveCursorPosition();
    }

    showEmojiPanel.value = true;
    emit("toggleEmojiPanel", true);

    // 确保不在语音模式
    isVoiceMode.value = false;

    // 延迟隐藏键盘，确保光标位置已保存
    setTimeout(() => {
      uni.hideKeyboard();
    }, 50);
  } else {
    // 如果是关闭表情面板，聚焦到输入框并恢复光标位置
    nextTick(() => {
      textareaFocus.value = true;
      // 延迟恢复光标位置
      setTimeout(() => {
        const textLength = inputText.value.length;
        if (textLength > 0) {
          // 验证保存的光标位置是否有效
          const savedStart = lastValidCursorPosition.value.start;
          const savedEnd = lastValidCursorPosition.value.end;

          if (
            savedStart >= 0 &&
            savedStart <= textLength &&
            savedEnd >= 0 &&
            savedEnd <= textLength
          ) {
            cursorPosition.value = { start: savedStart, end: savedEnd };
          } else {
            // 如果保存的位置无效，设置到文本末尾
            cursorPosition.value = { start: textLength, end: textLength };
          }
        }
      }, 150);
    });
  }
};

// 切换更多功能面板
const toggleMorePanel = () => {
  const wasShowing = showMorePanel.value;

  // 先隐藏所有面板
  hideAllPanels();

  // 如果不是关闭操作，打开更多面板
  if (!wasShowing) {
    // 在打开更多面板前，保存当前有效的光标位置
    if (textareaFocus.value || inputText.value.length > 0) {
      saveCursorPosition();
    }

    showMorePanel.value = true;
    emit("toggleMorePanel", true);

    // 确保不在语音模式
    isVoiceMode.value = false;

    // 延迟隐藏键盘，确保光标位置已保存
    setTimeout(() => {
      uni.hideKeyboard();
    }, 50);
  } else {
    // 如果是关闭更多面板，聚焦到输入框并恢复光标位置
    nextTick(() => {
      textareaFocus.value = true;
      // 延迟恢复光标位置
      setTimeout(() => {
        const textLength = inputText.value.length;
        if (textLength > 0) {
          // 验证保存的光标位置是否有效
          const savedStart = lastValidCursorPosition.value.start;
          const savedEnd = lastValidCursorPosition.value.end;

          if (
            savedStart >= 0 &&
            savedStart <= textLength &&
            savedEnd >= 0 &&
            savedEnd <= textLength
          ) {
            cursorPosition.value = { start: savedStart, end: savedEnd };
          } else {
            // 如果保存的位置无效，设置到文本末尾
            cursorPosition.value = { start: textLength, end: textLength };
          }
        }
      }, 150);
    });
  }
};

// 发送文本消息
const sendMessage = () => {
  if (!inputText.value.trim()) return;
  emit("sendText", inputText.value);
  inputText.value = "";

  // 重置光标位置
  cursorPosition.value = { start: 0, end: 0 };
  lastValidCursorPosition.value = { start: 0, end: 0 };

  // 重置输入框状态
  textareaHeight.value = 36;
  isTextareaExpanded.value = false;
  hideAllPanels();

  // 聚焦输入框
  nextTick(() => {
    textareaFocus.value = true;
  });
};

// 获取有效的光标位置
const getValidCursorPosition = (text: string) => {
  let { start, end } = cursorPosition.value;

  // 如果当前光标位置为0但文本不为空，尝试使用最后有效的光标位置
  if (start === 0 && end === 0 && text.length > 0) {
    const lastStart = lastValidCursorPosition.value.start;
    const lastEnd = lastValidCursorPosition.value.end;

    // 验证最后保存的光标位置是否有效
    if (lastStart >= 0 && lastStart <= text.length) {
      start = lastStart;
      end = Math.min(lastEnd, text.length);
    } else {
      // 如果最后保存的位置也无效，则设置到文本末尾
      start = end = text.length;
    }
  }

  // 确保光标位置在有效范围内
  start = Math.max(0, Math.min(start, text.length));
  end = Math.max(0, Math.min(end, text.length));

  return { start, end };
};

// 插入表情
const insertEmoji = (emoji: string) => {
  // 获取当前输入文本
  const text = inputText.value;

  // 获取有效的光标位置
  const { start, end } = getValidCursorPosition(text);

  // 在光标位置插入表情
  const newText = text.substring(0, start) + emoji + text.substring(end);
  inputText.value = newText;

  // 计算新的光标位置
  const newPosition = start + emoji.length;
  const newCursorPos = {
    start: newPosition,
    end: newPosition,
  };

  // 更新光标位置
  cursorPosition.value = newCursorPos;
  lastValidCursorPosition.value = newCursorPos;

  // 检查输入框扩展状态
  checkTextareaExpansion(newText);

  // 强制聚焦输入框并设置光标位置
  nextTick(() => {
    // 如果输入框没有聚焦，先聚焦
    if (!textareaFocus.value) {
      textareaFocus.value = true;
    }

    // 延迟设置光标位置，确保DOM更新完成
    setTimeout(() => {
      cursorPosition.value = newCursorPos;
    }, 100);
  });
};

// 语音录制开始
const onVoiceStart = () => {
  // 添加震动反馈
  // uni.vibrateShort()
  emit("startRecording");
};

// 语音录制完成
const onVoiceFinish = (duration: number) => {
  // 添加震动反馈
  // uni.vibrateShort()
  emit("endRecording", duration);
};

// 语音录制取消
const onVoiceCancel = () => {
  emit("endRecording", 0);
};

// 更新语音录制状态
const onVoiceStatusUpdate = (isCanceled: boolean) => {
  if (isCanceled) {
    // 进入取消状态时震动提示
    // uni.vibrateShort()
  }
  emit("updateRecordingStatus", isCanceled);
};

// 处理更多面板项点击
const onMoreItemClick = (type: string) => {
  emit("moreItemClick", type);
  hideAllPanels();
};

// 删除光标位置的字符
const deleteLastCharacter = () => {
  if (!inputText.value.length) return;

  const text = inputText.value;
  const { start, end } = getValidCursorPosition(text);

  let newText = text;
  let newCursorPos = { start, end };

  // 如果有选中文本，则删除选中部分
  if (start !== end) {
    newText = text.substring(0, start) + text.substring(end);
    newCursorPos = { start, end: start };
  }
  // 如果光标不在文本开头，则删除光标前的一个字符
  else if (start > 0) {
    const newPosition = start - 1;
    newText = text.substring(0, newPosition) + text.substring(start);
    newCursorPos = { start: newPosition, end: newPosition };
  }

  // 更新文本和光标位置
  inputText.value = newText;
  cursorPosition.value = newCursorPos;
  lastValidCursorPosition.value = newCursorPos;

  // 检查输入框扩展状态
  checkTextareaExpansion(newText);

  // 保持输入框焦点
  nextTick(() => {
    textareaFocus.value = true;
    // 延迟设置光标位置
    setTimeout(() => {
      cursorPosition.value = newCursorPos;
    }, 100);
  });
};

// 暴露给父组件的方法
defineExpose({
  insertEmoji,
  sendMessage,
  updateKeyboardHeightChange,
  hidedKeyboard,
});
</script>

<style lang="scss" scoped>
.chat-toolbar {
  position: relative;
}

.chat-input-bar {
  position: relative;
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #ffffff;
  border-top: 1rpx solid #e5e7eb;
  box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.04);

  .input-mode,
  .emoji-button,
  .more-button {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 16rpx;
    transition: transform 0.2s ease;

    &:active {
      transform: scale(0.95);
    }
  }

  .input-container {
    flex: 1;
    min-height: 80rpx;
    max-height: 80rpx;
    margin: 0 12rpx;
    padding: 0 24rpx;
    background-color: #f8fafc;
    border-radius: 40rpx;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    border: 2rpx solid transparent;
    transition: all 0.3s ease;

    &:focus-within {
      background-color: #ffffff;
      border-color: var(--primary-500);
      box-shadow: 0 0 0 4rpx var(--primary-50);
    }

    .input-textarea {
      width: 100%;
      min-height: 44rpx;
      max-height: 160rpx;
      padding: 18rpx 0;
      font-size: 32rpx;
      line-height: 1.4;
      caret-color: var(--primary-500);
      background-color: var(--pg-input);
      letter-spacing: 0.2rpx;
    }

    &.expanded {
      max-height: 200rpx;
      align-items: flex-start;
      border-radius: 24rpx;

      .input-textarea {
        padding: 18rpx 0;
      }
    }
  }

  .voice-container {
    flex: 1;
    height: 80rpx;
    padding: 0;
    margin: 0 12rpx;
    overflow: hidden;
    background-color: #f8fafc;
    border-radius: 40rpx;
  }
}
/* Emoji面板容器 */
.emoji-panel-container {
  position: relative;
  overflow: hidden;
  background-color: #ffffff;
  border-top: 1rpx solid #e5e7eb;
  transition: height 0.25s ease;
  transform-origin: bottom;
  /* #ifndef APP-NVUE */
  will-change: height;
  /* #endif */

  &.panel-show {
    animation: panel-slide-up 0.3s ease-in-out;
  }

  .emoji-panel {
    /* #ifndef APP-NVUE */
    display: flex;
    /* #endif */
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: flex-start;
    padding: 24rpx;
  }

  .emoji-item {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 88rpx;
    height: 88rpx;
    margin: 8rpx;
    font-size: 56rpx;
    text-align: center;
    border-radius: 16rpx;
    transition: all 0.2s ease;

    &:active {
      background-color: #f3f4f6;
      transform: scale(0.9);
    }
  }

  .emoji-footer {
    position: absolute;
    right: 20rpx;
    bottom: 20rpx;
    z-index: 2;
    display: flex;
    justify-content: space-between;
    width: 280rpx;

    .emoji-button-group {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
    }

    .emoji-delete-button {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 120rpx;
      height: 72rpx;
      background-color: #fff;
      border-radius: 16rpx;
      transition: opacity 0.2s;

      &:active {
        background-color: #f0f0f0;
      }

      &.disabled {
        cursor: not-allowed;
        opacity: 0.5;

        &:active {
          background-color: #fff;
        }
      }
    }

    .emoji-send-button {
      width: 120rpx;
      height: 72rpx;
      font-size: 28rpx;
      line-height: 72rpx;
      color: #fff;
      text-align: center;
      background-color: #1e80ff;
      border-radius: 16rpx;
      box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.1);
      transition: background-color 0.2s;

      &:active {
        background-color: #1670e5;
      }

      &.disabled {
        cursor: not-allowed;
        background-color: #c3d4e9;
      }
    }
  }
}
/* 更多功能面板容器 */
.more-panel-container {
  overflow: hidden;
  background-color: #ffffff;
  border-top: 1rpx solid #e5e7eb;
  transition: height 0.25s ease;
  transform-origin: bottom;
  /* #ifndef APP-NVUE */
  will-change: height;
  /* #endif */

  &.panel-show {
    animation: panel-slide-up 0.3s ease-in-out;
  }

  .more-grid {
    display: flex;
    flex-wrap: wrap;
    padding: 40rpx 20rpx;
  }

  .more-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 25%; /* 每行4个 */
    margin-bottom: 40rpx;

    .more-icon-wrapper {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 120rpx;
      height: 120rpx;
      margin-bottom: 16rpx;
      background-color: #f8fafc;
      border-radius: 28rpx;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
      transition: all 0.2s ease;

      &:active {
        background-color: #e5e7eb;
        transform: scale(0.95);
      }
    }

    text {
      font-size: 26rpx;
      color: #6b7280;
      font-weight: 500;
      letter-spacing: 0.2rpx;
    }
  }
}

@keyframes panel-slide-up {
  from {
    opacity: 0.8;
    transform: translateY(10%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.flex-1 {
  flex: 1;
  height: 100%;
}
</style>
