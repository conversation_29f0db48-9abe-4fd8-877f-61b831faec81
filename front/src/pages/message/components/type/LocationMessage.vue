<template>
  <view class="location-message" :class="{ self: self }" @click="openLocation">
    <image class="location-image" :src="imageUrl" mode="aspectFill" />
    <view class="location-info">
      <text class="location-name">{{ name }}</text>
      <text class="location-address">{{ address }}</text>
    </view>
  </view>
</template>

<script setup lang="ts">
const props = defineProps<{
  name: string // 位置名称
  address: string // 位置详细地址
  latitude: number // 纬度
  longitude: number // 经度
  imageUrl: string // 地图截图
  self?: boolean // 是否为自己发送的消息
}>()

const emit = defineEmits<{
  (e: 'open', location: any): void
}>()

// 打开位置
const openLocation = () => {
  uni.openLocation({
    latitude: props.latitude,
    longitude: props.longitude,
    name: props.name,
    address: props.address,
    success: () => {
      console.log('成功打开位置')
      emit('open', {
        latitude: props.latitude,
        longitude: props.longitude,
        name: props.name,
        address: props.address,
      })
    },
    fail: (err) => {
      console.error('打开位置失败', err)
      uni.showToast({
        title: '打开位置失败',
        icon: 'none',
      })
    },
  })
}
</script>

<style lang="scss" scoped>
.location-message {
  align-self: flex-start;
  width: 450rpx;
  overflow: hidden;
  background-color: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 0 4rpx rgba(0, 0, 0, 0.1);

  &.self {
    align-self: flex-end;
  }

  .location-image {
    width: 100%;
    height: 200rpx;
    object-fit: cover;
  }

  .location-info {
    padding: 20rpx;

    .location-name {
      display: block;
      margin-bottom: 8rpx;
      font-size: 28rpx;
      font-weight: bold;
      color: #333333;
    }

    .location-address {
      display: block;
      font-size: 24rpx;
      color: #999999;
    }
  }
}
</style>
