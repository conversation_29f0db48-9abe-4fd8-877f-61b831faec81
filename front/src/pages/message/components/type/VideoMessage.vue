<template>
  <view class="video-message" :class="{ self: self }" @click="onVideoClick">
    <!-- 加载中占位图 -->
    <view v-if="isLoading && !isError" class="video-placeholder loading-placeholder">
      <uni-icons type="videocam" size="30" color="#cccccc" />
      <text class="placeholder-text">视频加载中...</text>
    </view>

    <!-- 加载失败占位图 -->
    <view v-else-if="isError" class="video-placeholder error-placeholder" @click.stop="reloadVideo">
      <uni-icons type="videocam-filled" size="30" color="#999999" />
      <text class="placeholder-text">加载失败，点击重试</text>
    </view>

    <!-- 正常显示视频封面 -->
    <template v-else>
      <image
        class="video-poster"
        mode="aspectFill"
        :src="posterUrl"
        @load="onPosterLoad"
        @error="onPosterError"
      />
      <view class="video-overlay">
        <view class="play-icon-box">
          <uni-icons type="videocam" size="24" color="#FFFFFF" />
        </view>
        <view class="video-duration" v-if="duration > 0">{{ formatDuration }}</view>
      </view>
    </template>
  </view>
</template>

<script setup lang="ts">
import { computed, ref, onMounted } from 'vue'

const props = defineProps<{
  src: string
  poster?: string
  width?: number
  height?: number
  duration?: number
  self?: boolean // 是否为自己发送的消息
}>()

// 视频信息
const posterUrl = ref(props.poster || '')
const videoWidth = ref(props.width || 300)
const videoHeight = ref(props.height || 240)
const isLoading = ref(true)
const isError = ref(false)
const defaultPoster = '/static/images/video-placeholder.png'

// 格式化视频时长显示
const formatDuration = computed(() => {
  if (!props.duration) return '00:00'

  const minutes = Math.floor(props.duration / 60)
  const seconds = Math.floor(props.duration % 60)

  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
})

// 生成视频封面
onMounted(() => {
  if (!props.poster && props.src) {
    generateVideoPoster()
  } else {
    // 使用提供的海报
    isLoading.value = false
  }
})

// 在没有封面时尝试生成封面
const generateVideoPoster = () => {
  isLoading.value = true
  isError.value = false

  // 检查是否本地视频
  if (props.src.startsWith('blob:') || props.src.startsWith('file://')) {
    // 本地视频，使用默认封面
    posterUrl.value = defaultPoster
    isLoading.value = false
    return
  }

  // 远程视频，尝试使用远程服务获取封面
  try {
    const url = new URL(props.src, window.location.href)
    const thumbParam = 'vframe/jpg/offset/1/w/400'

    if (url.search) {
      posterUrl.value = `${props.src}&${thumbParam}`
    } else {
      posterUrl.value = `${props.src}?${thumbParam}`
    }
  } catch (error) {
    console.error('解析视频URL失败', error)
    posterUrl.value = defaultPoster
    isLoading.value = false
  }
}

// 封面加载成功
const onPosterLoad = () => {
  isLoading.value = false
  isError.value = false
}

// 封面加载失败
const onPosterError = () => {
  console.error('视频封面加载失败:', posterUrl.value)
  // 如果不是默认封面，则尝试使用默认封面
  if (posterUrl.value !== defaultPoster) {
    posterUrl.value = defaultPoster
  } else {
    isError.value = true
    isLoading.value = false
  }
}

// 重新加载视频
const reloadVideo = () => {
  isLoading.value = true
  isError.value = false
  generateVideoPoster()

  // 创建新的Image对象尝试预加载封面
  const img = new Image()
  img.onload = () => {
    isLoading.value = false
    isError.value = false
  }
  img.onerror = () => {
    posterUrl.value = defaultPoster
    isLoading.value = false
  }
  img.src = posterUrl.value
}

// 视频点击处理
const onVideoClick = () => {
  if (isError.value) {
    reloadVideo()
    return
  }

  if (!isLoading.value && !isError.value) {
    playVideo()
  }
}

// 播放视频
const playVideo = () => {
  // 在小程序和App中使用原生视频播放
  uni.navigateTo({
    url: `/pages/common/video-player?url=${encodeURIComponent(props.src)}`,
    success: () => {
      console.log('成功打开视频播放页')
    },
    fail: (err) => {
      console.error('打开视频播放页失败', err)
      // 播放失败时尝试使用系统播放器
      fallbackToSystemPlayer()
    },
  })
}

// 回退到系统播放器
const fallbackToSystemPlayer = () => {
  try {
    // 检查是否在App环境中
    // #ifdef APP-PLUS
    // 打开系统播放器
    plus.runtime.openFile(props.src, {}, (err) => {
      uni.showToast({
        title: '播放视频失败',
        icon: 'none',
      })
      console.error('播放视频失败', err)
    })
    // #endif

    // #ifndef APP-PLUS
    uni.showToast({
      title: '此环境不支持视频播放',
      icon: 'none',
    })
    // #endif
  } catch (e) {
    uni.showToast({
      title: '播放视频失败',
      icon: 'none',
    })
    console.error('播放视频失败', e)
  }
}
</script>

<style lang="scss" scoped>
.video-message {
  position: relative;
  align-self: flex-start;
  width: 300rpx;
  height: 240rpx;
  overflow: hidden;
  border-radius: 12rpx;
  box-shadow: 0 0 4rpx rgba(0, 0, 0, 0.1);

  &.self {
    align-self: flex-end;
  }

  .video-poster {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .video-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background-color: #f5f5f5;

    .placeholder-text {
      margin-top: 10rpx;
      font-size: 24rpx;
      color: #999999;
    }
  }

  .loading-placeholder {
    background-color: #f8f8f8;
  }

  .error-placeholder {
    cursor: pointer;
    background-color: #f0f0f0;

    &:active {
      opacity: 0.8;
    }
  }

  .video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.2);

    .play-icon-box {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 80rpx;
      height: 80rpx;
      background-color: rgba(0, 0, 0, 0.5);
      border: 2rpx solid #ffffff;
      border-radius: 50%;
    }

    .video-duration {
      position: absolute;
      right: 20rpx;
      bottom: 20rpx;
      padding: 4rpx 12rpx;
      font-size: 24rpx;
      color: #ffffff;
      background-color: rgba(0, 0, 0, 0.5);
      border-radius: 10rpx;
    }
  }
}
</style>
