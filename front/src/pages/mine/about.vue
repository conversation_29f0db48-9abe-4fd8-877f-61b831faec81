<template>
  <view class="about-page">
    <view class="content">
      <!-- App信息 -->
      <view class="app-info">
        <view class="app-logo">
          <image src="/static/logo.png" mode="aspectFit" />
        </view>
        <text class="app-name">本地宝</text>
        <text class="app-version">v1.0.0</text>
        <text class="app-desc">本地生活服务平台，连接你我他</text>
      </view>

      <!-- 核心功能简介 -->
      <view class="features-section">
        <view class="section-title">核心服务</view>
        <view class="features-grid">
          <view class="feature-item">
            <text
              class="i-solar-briefcase-linear text-24rpx text-blue-500"
            ></text>
            <text class="feature-title">求职招聘</text>
          </view>

          <view class="feature-item">
            <text
              class="i-solar-home-2-linear text-24rpx text-green-500"
            ></text>
            <text class="feature-title">房产服务</text>
          </view>

          <view class="feature-item">
            <text class="i-solar-heart-linear text-24rpx text-pink-500"></text>
            <text class="feature-title">相亲交友</text>
          </view>

          <view class="feature-item">
            <text
              class="i-solar-settings-linear text-24rpx text-orange-500"
            ></text>
            <text class="feature-title">本地服务</text>
          </view>
        </view>
      </view>

      <!-- 联系我们 -->
      <view class="contact-section">
        <view class="section-title">联系我们</view>
        <view class="contact-list">
          <tui-list-cell padding="24rpx 32rpx" @click="copyEmail">
            <view class="list-item-content">
              <view class="icon-wrapper">
                <text
                  class="i-solar-letter-linear text-20rpx text-blue-500"
                ></text>
              </view>
              <view class="item-info">
                <text class="item-title">客服邮箱</text>
                <text class="item-note"><EMAIL></text>
              </view>
              <view class="item-right">
                <text
                  class="i-solar-copy-linear text-16rpx text-gray-400"
                ></text>
              </view>
            </view>
          </tui-list-cell>
          <tui-list-cell padding="24rpx 32rpx" @click="copyPhone">
            <view class="list-item-content">
              <view class="icon-wrapper">
                <text
                  class="i-solar-phone-linear text-20rpx text-green-500"
                ></text>
              </view>
              <view class="item-info">
                <text class="item-title">客服热线</text>
                <text class="item-note">************</text>
              </view>
              <view class="item-right">
                <text
                  class="i-solar-copy-linear text-16rpx text-gray-400"
                ></text>
              </view>
            </view>
          </tui-list-cell>
        </view>
      </view>

      <!-- 法律信息 -->
      <view class="legal-section">
        <view class="section-title">法律信息</view>
        <view class="legal-list">
          <tui-list-cell arrow padding="24rpx 32rpx" @click="goToPrivacyPolicy">
            <view class="list-item-content">
              <view class="icon-wrapper">
                <text
                  class="i-solar-document-linear text-20rpx text-indigo-500"
                ></text>
              </view>
              <view class="item-info">
                <text class="item-title">隐私政策</text>
              </view>
            </view>
          </tui-list-cell>
          <tui-list-cell arrow padding="24rpx 32rpx" @click="goToUserAgreement">
            <view class="list-item-content">
              <view class="icon-wrapper">
                <text
                  class="i-solar-file-text-linear text-20rpx text-purple-500"
                ></text>
              </view>
              <view class="item-info">
                <text class="item-title">用户协议</text>
              </view>
            </view>
          </tui-list-cell>
        </view>
      </view>

      <!-- 版权信息 -->
      <view class="copyright">
        <text class="copyright-text">© 2024 本地宝科技有限公司</text>
        <text class="copyright-text">All rights reserved</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import CustomNavBar from "@/components/CustomNavBar.vue";

// 复制邮箱
const copyEmail = () => {
  uni.setClipboardData({
    data: "<EMAIL>",
    success: () => {
      uni.showToast({
        title: "邮箱已复制",
        icon: "success",
      });
    },
  });
};

// 复制电话
const copyPhone = () => {
  uni.setClipboardData({
    data: "************",
    success: () => {
      uni.showToast({
        title: "电话已复制",
        icon: "success",
      });
    },
  });
};

// 隐私政策
const goToPrivacyPolicy = () => {
  uni.showToast({
    title: "隐私政策页面开发中",
    icon: "none",
  });
};

// 用户协议
const goToUserAgreement = () => {
  uni.showToast({
    title: "用户协议页面开发中",
    icon: "none",
  });
};
</script>

<style lang="scss" scoped>
.about-page {
  background-color: var(--bg-page);
  min-height: 100vh;
}

.content {
  padding: 32rpx;
}

.app-info {
  text-align: center;
  padding: 80rpx 0 60rpx;

  .app-logo {
    width: 160rpx;
    height: 160rpx;
    margin: 0 auto 32rpx;
    border-radius: 32rpx;
    overflow: hidden;
    background: var(--bg-card);
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);

    image {
      width: 100%;
      height: 100%;
    }
  }

  .app-name {
    display: block;
    font-size: 48rpx;
    font-weight: 700;
    color: var(--text-base);
    margin-bottom: 16rpx;
  }

  .app-version {
    display: block;
    font-size: 28rpx;
    color: var(--text-secondary);
    margin-bottom: 24rpx;
  }

  .app-desc {
    display: block;
    font-size: 26rpx;
    color: var(--text-info);
    line-height: 1.6;
  }
}

.features-section {
  margin-bottom: 48rpx;

  .section-title {
    font-size: 28rpx;
    font-weight: 600;
    color: var(--text-base);
    margin-bottom: 32rpx;
    text-align: center;
  }

  .features-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24rpx;

    .feature-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 16rpx;
      padding: 32rpx 24rpx;
      background-color: var(--bg-card);
      border-radius: var(--radius-lg);
      box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);

      .feature-title {
        font-size: 26rpx;
        color: var(--text-base);
        font-weight: 500;
      }
    }
  }
}

.contact-section,
.legal-section {
  margin-bottom: 48rpx;

  .section-title {
    font-size: 28rpx;
    font-weight: 600;
    color: var(--text-base);
    margin-bottom: 24rpx;
    padding-left: 8rpx;
  }
}

.contact-list,
.legal-list {
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
  overflow: hidden;
}

.list-item-content {
  display: flex;
  align-items: center;
  padding: 0 32rpx;
}

.icon-wrapper {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

.item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.item-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-base);
  margin-bottom: 4rpx;
}

.item-note {
  font-size: 24rpx;
  color: var(--text-secondary);
}

.item-right {
  display: flex;
  align-items: center;
  margin-left: 24rpx;
}

.copyright {
  text-align: center;
  padding: 40rpx 0;
  border-top: 1rpx solid #f5f5f5;

  .copyright-text {
    display: block;
    font-size: 22rpx;
    color: var(--text-info);
    line-height: 1.6;

    &:first-child {
      margin-bottom: 8rpx;
    }
  }
}
</style>
