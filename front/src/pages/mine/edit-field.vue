<template>
  <view class="edit-field-page">
    <view class="input-section">
      <input
        v-model="nickname"
        class="nickname-input"
        placeholder="请输入2-15个字的昵称"
        :maxlength="15"
      />
      <view class="clear-icon" v-if="nickname" @click="nickname = ''">
        <text class="i-carbon-close-filled"></text>
      </view>
    </view>

    <view class="quick-fill-section">
      <button class="wechat-nickname-btn" @click="fillWithWechatNickname">
        一键填充微信昵称
      </button>
    </view>

    <view class="notice-section">
      <text class="notice-text">
        好的昵称更容易获得大家的关注，支持中英文、数字
      </text>
    </view>

    <view class="save-btn-container">
      <button
        class="save-btn"
        :disabled="!isNicknameValid"
        @click="saveNickname"
      >
        保存
      </button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import { useUserStore } from "@/stores/user";

const userStore = useUserStore();
const nickname = ref("");

onLoad((options) => {
  if (options && options.value) {
    nickname.value = decodeURIComponent(options.value);
  }
});

const isNicknameValid = computed(() => {
  const len = nickname.value.trim().length;
  return len >= 2 && len <= 15;
});

const fillWithWechatNickname = () => {
  uni.getUserProfile({
    desc: "用于完善会员资料",
    success: (res) => {
      let wechatNickname = res.userInfo.nickName.slice(0, 15);
      nickname.value = wechatNickname;
    },
    fail: () => {
      uni.showToast({
        title: "获取微信昵称失败",
        icon: "none",
      });
    },
  });
};

const saveNickname = () => {
  if (!isNicknameValid.value) {
    uni.showToast({
      title: "昵称长度应为2-15个字符",
      icon: "none",
    });
    return;
  }

  // TODO: 调用API更新后端数据
  userStore.updateUserInfo({ nickname: nickname.value });

  uni.showToast({
    title: "昵称更新成功",
    icon: "success",
  });

  setTimeout(() => {
    uni.navigateBack();
  }, 1000);
};
</script>

<style lang="scss" scoped>
.edit-field-page {
  padding: 32rpx;
  background-color: var(--bg-page);
  min-height: 100vh;
}

.input-section {
  position: relative;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
}

.nickname-input {
  flex: 1;
  font-size: 30rpx;
  color: var(--text-primary);
}

.clear-icon {
  padding: 8rpx;
  color: var(--text-secondary);
}

.quick-fill-section {
  margin-top: 24rpx;
}

.wechat-nickname-btn {
  background: none;
  color: var(--primary);
  font-size: 26rpx;
  text-align: left;
  padding: 0;
  &::after {
    border: none;
  }
}

.notice-section {
  margin-top: 16rpx;
}

.notice-text {
  font-size: 24rpx;
  color: var(--text-info);
}

.save-btn-container {
  margin-top: 64rpx;
}

.save-btn {
  background-color: var(--primary);
  color: #fff;
  border-radius: 48rpx;
  font-size: 32rpx;

  &[disabled] {
    background-color: var(--primary-disabled);
    color: var(--text-on-disabled);
  }
}
</style>
