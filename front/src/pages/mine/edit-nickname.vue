<template>
  <view class="edit-nickname-page">
    <view class="content">
      <!-- 输入区域 -->
      <view class="input-section">
        <input
          v-model="newNickname"
          class="nickname-input"
          placeholder="请输入新昵称"
          maxlength="20"
          :focus="inputFocus"
          @input="onInput"
        />
        <view class="clear-icon" v-if="newNickname" @click="newNickname = ''">
          <text class="i-carbon-close-filled"></text>
        </view>
      </view>

      <!-- 微信一键填充 -->
      <view class="quick-fill-section">
        <button class="wechat-nickname-btn" @click="getWechatNickname">
          一键填充微信昵称
        </button>
      </view>

      <!-- 提示信息 -->
      <view class="notice-section">
        <text class="notice-text">
          好的昵称更容易获得大家的关注，支持中英文、数字
        </text>
      </view>
    </view>

    <!-- 底部操作按钮 -->
    <view class="bottom-actions">
      <button
        class="save-btn"
        :class="{ disabled: !canSave }"
        :disabled="!canSave"
        @click="saveNickname"
      >
        保存
      </button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { useUserStore } from "@/stores/user";
import CustomNavBar from "@/components/CustomNavBar.vue";

const userStore = useUserStore();

// 获取传入的当前昵称
const currentNickname = ref("");
const newNickname = ref("");
const inputFocus = ref(false);

// 计算是否可以保存
const canSave = computed(() => {
  const trimmed = newNickname.value.trim();
  return (
    trimmed &&
    trimmed !== currentNickname.value &&
    trimmed.length >= 1 &&
    trimmed.length <= 20
  );
});

// 页面加载时获取当前昵称
onMounted(() => {
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1] as any;
  const options = currentPage.options || {};

  if (options.value) {
    currentNickname.value = decodeURIComponent(options.value);
    newNickname.value = currentNickname.value;
  }

  // 自动聚焦输入框
  setTimeout(() => {
    inputFocus.value = true;
  }, 300);
});

// 输入事件处理
const onInput = (e: any) => {
  newNickname.value = e.detail.value;
};

// 获取微信昵称
const getWechatNickname = () => {
  uni.getUserProfile({
    desc: "用于完善用户资料",
    success: (res) => {
      if (res.userInfo && res.userInfo.nickName) {
        // 限制长度为20字符
        newNickname.value = res.userInfo.nickName.slice(0, 20);
        uni.showToast({
          title: "获取成功",
          icon: "success",
        });
      } else {
        uni.showToast({
          title: "未获取到微信昵称",
          icon: "none",
        });
      }
    },
    fail: (err) => {
      if (err.errMsg.includes("auth deny")) {
        uni.showToast({
          title: "需要授权才能获取微信昵称",
          icon: "none",
        });
      } else {
        uni.showToast({
          title: "获取失败，请稍后重试",
          icon: "none",
        });
      }
    },
  });
};

// 保存昵称
const saveNickname = () => {
  if (!canSave.value) return;

  const trimmedNickname = newNickname.value.trim();

  // 简单的内容验证
  if (trimmedNickname.length < 1) {
    uni.showToast({
      title: "昵称不能为空",
      icon: "none",
    });
    return;
  }

  if (trimmedNickname.length > 20) {
    uni.showToast({
      title: "昵称长度不能超过20个字符",
      icon: "none",
    });
    return;
  }

  // 更新用户信息
  userStore.updateUserInfo({ nickname: trimmedNickname });

  uni.showToast({
    title: "保存成功",
    icon: "success",
  });

  // 延迟返回上一页
  setTimeout(() => {
    uni.navigateBack();
  }, 1000);
};
</script>

<style lang="scss" scoped>
.edit-nickname-page {
  background-color: var(--bg-page);
  min-height: 100vh;
}

.content {
  padding: 32rpx;
}

.input-section {
  position: relative;
  background-color: var(--bg-card);
  border-radius: var(--radius);
  padding: 24rpx;
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.nickname-input {
  flex: 1;
  font-size: 30rpx;
  color: var(--text-base);
}

.clear-icon {
  padding: 8rpx;
  color: var(--text-secondary);
  font-size: 20rpx;
}

.quick-fill-section {
  margin-bottom: 16rpx;
}

.wechat-nickname-btn {
  background: none;
  color: var(--primary);
  font-size: 26rpx;
  text-align: left;
  padding: 0;
  &::after {
    border: none;
  }
}

.notice-section {
  margin-bottom: 64rpx;
}

.notice-text {
  font-size: 24rpx;
  color: var(--text-info);
  line-height: 1.5;
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 32rpx;
  background-color: var(--bg-page);
  border-top: 1rpx solid var(--border-color);
  padding-bottom: calc(32rpx + env(safe-area-inset-bottom));
}

.save-btn {
  width: 100%;
  background-color: var(--primary);
  color: var(--text-inverse);
  border-radius: 48rpx;
  font-size: 32rpx;
  font-weight: var(--font-weight-medium);
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;

  &.disabled {
    background-color: var(--text-disable);
    color: var(--text-grey);
  }

  &::after {
    border: none;
  }
}
</style>
