<template>
  <PageLayout :show-nav-bar="false" :show-login-tip="false">
    <view class="mine-page" :class="{ 'is-login': isLoggedIn }">
      <!-- 背景渐变 -->
      <view class="page-background"></view>

      <uni-nav-bar
        :border="false"
        :fixed="true"
        :background-color="'transparent'"
        status-bar="true"
      />

      <view class="main-content">
        <!-- 用户信息卡片 -->
        <view class="user-card" @click="handleUserCardClick">
          <view class="user-info">
            <image :src="userAvatar" class="avatar" mode="aspectFill" />
            <view class="details">
              <view class="name-section">
                <text class="name">{{ displayName }}</text>
                <view v-if="isLoggedIn && user?.isVip" class="vip-tag"
                  >VIP</view
                >
              </view>
              <text v-if="!isLoggedIn" class="intro"
                >登录后解锁更多精彩内容</text
              >
            </view>
          </view>
          <view class="arrow">
            <text
              class="i-solar-alt-arrow-right-linear text-40rpx text-grey"
            ></text>
          </view>
        </view>

        <!-- 用户数据统计 -->
        <view class="stats-card">
          <view
            class="stat-item"
            @click="secureNavigateToWithDirectLogin('/pages/post/mine')"
          >
            <text class="value">{{ isLoggedIn ? user?.posts || 0 : "-" }}</text>
            <text class="label">我的发布</text>
          </view>
          <view
            class="stat-item"
            @click="secureNavigateToWithDirectLogin('/pages/mine/collections')"
          >
            <text class="value">{{
              isLoggedIn ? user?.followers || 0 : "-"
            }}</text>
            <text class="label">收藏</text>
          </view>
          <view
            class="stat-item"
            @click="secureNavigateToWithDirectLogin('/pages/mine/history')"
          >
            <text class="value">{{
              isLoggedIn ? user?.following || 0 : "-"
            }}</text>
            <text class="label">足迹</text>
          </view>
        </view>

        <!-- 我的钱包  暂时不开放-->
        <!-- <view class="wallet-card" @click="secureNavigate('/pages/mine/wallet')">
          <view class="wallet-bg-pattern"></view>
          <view class="wallet-content">
            <view class="wallet-header">
              <view class="wallet-title-section">
                <text
                  class="i-solar-wallet-linear text-24rpx text-white mr-2"
                ></text>
                <text class="wallet-title">我的钱包</text>
              </view>
              <view class="wallet-actions">
                <text class="wallet-action-btn">
                  <text
                    class="i-solar-receipt-linear text-16rpx text-white mr-1"
                  ></text>
                  <text class="action-text">账单</text>
                </text>
                <text
                  class="i-solar-arrow-right-linear text-16rpx text-white ml-2"
                ></text>
              </view>
            </view>
            <view class="wallet-balance">
              <template v-if="isLoggedIn">
                <view class="balance-section">
                  <text class="balance-label">余额</text>
                  <view class="balance-amount">
                    <text class="currency">¥</text>
                    <text class="amount">{{
                      user?.balance?.toFixed(2) || "0.00"
                    }}</text>
                  </view>
                </view>
              </template>
              <template v-else>
                <view class="login-section">
                  <text class="login-prompt">登录后查看余额</text>
                  <view class="login-hint">
                    <text
                      class="i-solar-lock-linear text-16rpx text-white/60 mr-1"
                    ></text>
                    <text class="hint-text">安全保护</text>
                  </view>
                </view>
              </template>
            </view>
          </view>
        </view> -->

        <!-- 核心功能区 -->
        <view class="section-card">
          <view class="grid-menu">
            <view
              v-for="item in coreFeatures"
              :key="item.title"
              class="grid-item"
              @click="handleCoreFeatureClick(item)"
            >
              <view class="grid-icon-wrapper">
                <text
                  :class="item.iconClass"
                  class="text-42rpx"
                  :style="{ color: item.iconColor }"
                ></text>
              </view>
              <text class="grid-label">{{ item.title }}</text>
            </view>
          </view>
        </view>

        <!-- 通用功能区 -->
        <view class="section-card">
          <view class="section-title">通用设置</view>
          <view class="list-container">
            <tui-list-cell
              v-for="item in otherFeatures"
              :key="item.title"
              :hover="false"
              padding="32rpx 32rpx"
              arrow
              @click="handleMenuItemClick(item)"
            >
              <view class="list-item-content">
                <view class="list-icon-wrapper">
                  <text
                    :class="item.iconClass"
                    class="text-32rpx"
                    :style="{ color: item.iconColor }"
                  ></text>
                </view>
                <text class="list-item-text">{{ item.title }}</text>
              </view>
            </tui-list-cell>
          </view>
        </view>
      </view>
    </view>
  </PageLayout>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { useUserStore } from "@/stores/user";
import PageLayout from "@/components/PageLayout.vue";
import { navigateTo, secureNavigateToWithDirectLogin } from "@/utils";

const userStore = useUserStore();

const isLoggedIn = computed(() => userStore.isLogin);
const user = computed(() => userStore.user);

const displayName = computed(() => {
  return isLoggedIn.value ? userStore.displayName : "您好，请登录";
});

onShow(() => {
  // uni.$emit("onLogin", true);
});

const userAvatar = computed(() => {
  return isLoggedIn.value
    ? userStore.userAvatar
    : "/static/images/default-avatar.png";
});

// 核心功能菜单
const coreFeatures = [
  {
    title: "我的发布",
    iconClass: "i-solar-document-text-linear",
    bgColor: "#EBF5FF",
    iconColor: "#3B82F6",
    path: "/pages/post/mine",
  },
  {
    title: "我的收藏",
    iconClass: "i-solar-heart-linear",
    bgColor: "#FFFBEB",
    iconColor: "#F59E0B",
    path: "/pages/mine/collections",
  },
  {
    title: "浏览历史",
    iconClass: "i-solar-clock-circle-linear",
    bgColor: "#ECFDF5",
    iconColor: "#10B981",
    path: "/pages/mine/history",
  },
  {
    title: "在线客服",
    iconClass: "i-solar-chat-round-dots-linear",
    bgColor: "#EFF6FF",
    iconColor: "#6366F1",
    action: "contact",
  },
];

// 其他功能菜单
const otherFeatures = [
  {
    title: "认证中心",
    iconClass: "i-solar-user-check-rounded-bold-duotone",
    bgColor: "#EBF5FF",
    iconColor: "#3B82F6",
    path: "/pages/verification/index",
    action: "navigate",
  },
  {
    title: "账号设置",
    iconClass: "i-solar-settings-linear",
    bgColor: "#EFF6FF",
    iconColor: "#6366F1",
    path: "/pages/mine/settings",
    action: "navigate",
  },
  {
    title: "隐私设置",
    iconClass: "i-solar-shield-check-linear",
    bgColor: "#F0F9FF",
    iconColor: "#0EA5E9",
    path: "/pages/mine/privacy",
    action: "navigate",
  },
  {
    title: "关于我们",
    iconClass: "i-solar-info-circle-linear",
    bgColor: "#F3F4F6",
    iconColor: "#6B7280",
    path: "/pages/mine/about",
    action: "navigate",
  },
  {
    title: "退出登录",
    iconClass: "i-solar-exit-linear",
    bgColor: "#FEF2F2",
    iconColor: "#EF4444",
    path: "",
    action: "logout",
  },
];

// 用户卡片点击
const handleUserCardClick = () => {
  if (isLoggedIn.value) {
    navigateTo("/pages/mine/profile");
  }
};

// 核心功能点击处理
const handleCoreFeatureClick = (item: (typeof coreFeatures)[0]) => {
  if (item.action === "contact") {
    uni.showToast({
      title: "客服功能开发中",
      icon: "none",
    });
  } else if (item.path) {
    secureNavigateToWithDirectLogin(item.path);
  }
};

// 菜单项点击处理
const handleMenuItemClick = (item: (typeof otherFeatures)[0]) => {
  if (item.action === "navigate") {
    secureNavigateToWithDirectLogin(item.path);
  } else if (item.action === "contact") {
    uni.makePhoneCall({
      phoneNumber: "18888888888", // 在这里替换为实际的客服电话
    });
  } else if (item.action === "logout") {
    if (!isLoggedIn.value) return;

    uni.showModal({
      title: "提示",
      content: "确定要退出登录吗？",
      success: (res) => {
        if (res.confirm) {
          userStore.clearUserInfo();
        }
      },
    });
  }
};
</script>

<style lang="scss" scoped>
.mine-page {
  position: relative;
  min-height: 100vh;
  padding-bottom: 48rpx;
  background-color: var(--bg-page);
  overflow-x: hidden;

  &.is-login {
    .page-background {
      height: 420rpx;
    }
  }
}

.page-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 250rpx;
  background: linear-gradient(
    175deg,
    #dbeafe 5%,
    #eef2ff 40%,
    var(--bg-page) 80%
  );
  transition: height 0.4s ease-out;
  z-index: 0;
}

.main-content {
  position: relative;
  z-index: 1;
  padding: 0 32rpx;
}

.user-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 0;
  margin-bottom: 16rpx;

  .user-info {
    display: flex;
    align-items: center;
    gap: 24rpx;

    .avatar {
      width: 120rpx;
      height: 120rpx;
      border-radius: 50%;
      border: 4rpx solid #ffffff;
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
    }

    .details {
      .name-section {
        display: flex;
        align-items: center;
        gap: 16rpx;
      }
      .name {
        font-size: 40rpx;
        font-weight: 600;
        color: var(--text-base);
      }
      .vip-tag {
        background: linear-gradient(135deg, #fde047, #facc15);
        color: #854d0e;
        padding: 4rpx 12rpx;
        border-radius: 8rpx;
        font-size: 20rpx;
        font-weight: bold;
      }
      .intro {
        font-size: 24rpx;
        color: var(--text-secondary);
        margin-top: 8rpx;
      }
    }
  }
}

.stats-card {
  display: flex;
  justify-content: space-around;
  padding: 20rpx;
  border-radius: var(--radius-lg);
  margin-bottom: 32rpx;

  .stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8rpx;
    .value {
      font-size: 36rpx;
      font-weight: 600;
      color: var(--text-base);
    }
    .label {
      font-size: 26rpx;
      color: var(--text-info);
    }
  }
}

.wallet-card {
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 24rpx;
  padding: 0;
  margin: 0 0 32rpx;
  overflow: hidden;
  cursor: pointer;

  .wallet-bg-pattern {
    position: absolute;
    top: -50rpx;
    right: -50rpx;
    width: 200rpx;
    height: 200rpx;
    background: radial-gradient(
      circle,
      rgba(255, 255, 255, 0.08) 0%,
      transparent 70%
    );
    border-radius: 50%;
  }

  .wallet-content {
    position: relative;
    z-index: 2;
    padding: 32rpx;
  }

  .wallet-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24rpx;

    .wallet-title-section {
      display: flex;
      align-items: center;

      .wallet-title {
        font-size: 28rpx;
        font-weight: 500;
        color: #fff;
      }
    }

    .wallet-actions {
      display: flex;
      align-items: center;

      .wallet-action-btn {
        display: flex;
        align-items: center;
        background-color: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(10rpx);
        padding: 8rpx 16rpx;
        border-radius: 20rpx;

        .action-text {
          font-size: 24rpx;
          color: #fff;
        }
      }
    }
  }

  .wallet-balance {
    .balance-section {
      .balance-label {
        font-size: 22rpx;
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: 8rpx;
        display: block;
      }

      .balance-amount {
        display: flex;
        align-items: baseline;

        .currency {
          font-size: 28rpx;
          color: #fff;
          margin-right: 4rpx;
          font-weight: 500;
        }

        .amount {
          font-size: 48rpx;
          color: #fff;
          font-weight: 700;
          letter-spacing: 1rpx;
        }
      }
    }

    .login-section {
      .login-prompt {
        font-size: 32rpx;
        color: rgba(255, 255, 255, 0.9);
        font-weight: 500;
        margin-bottom: 8rpx;
        display: block;
      }

      .login-hint {
        display: flex;
        align-items: center;

        .hint-text {
          font-size: 22rpx;
          color: rgba(255, 255, 255, 0.6);
        }
      }
    }
  }
}

.section-card {
  background-color: var(--bg-card);
  padding: 32rpx;
  border-radius: var(--radius-lg);
  box-shadow: 0 8rpx 40rpx rgba(var(--primary-rgb), 0.06);
  margin-bottom: 32rpx;

  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    margin-bottom: 32rpx;
    color: var(--text-base);
  }
}

.grid-menu {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 32rpx;

  .grid-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16rpx;

    .grid-icon-wrapper {
      border-radius: var(--radius-lg);
      display: flex;
      justify-content: center;
      align-items: center;
      transition: transform 0.2s ease;
    }

    &:active .grid-icon-wrapper {
      transform: scale(0.92);
    }

    .grid-label {
      font-size: 26rpx;
    }
  }
}

.list-container {
  margin: 0 -32rpx;
}

.list-item-content {
  display: flex;
  align-items: center;
  width: 100%;
}

.list-icon-wrapper {
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.list-item-text {
  font-size: 30rpx;
  color: var(--text-base);
}
</style>
