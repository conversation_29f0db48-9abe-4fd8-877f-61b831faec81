<template>
  <view class="container">
    <uni-nav-bar
      :fixed="true"
      :border="false"
      status-bar
      left-icon="back"
      title="动态详情"
      @clickLeft="goBack"
    />

    <scroll-view
      scroll-y
      class="content-scroll"
      :style="{ height: scrollViewHeight }"
    >
      <!-- 动态内容 -->
      <view class="post-wrapper">
        <post-item
          :post="postDetail"
          @like="handleLike"
          @comment="openCommentInput"
          @share="handleShare"
          @follow="handleFollow"
          @unfollow="handleUnfollow"
          @preview-image="previewImage"
        />
      </view>

      <!-- 评论区域 -->
      <view class="comments-section bg-white mx-20rpx rounded-lg mb-120rpx">
        <view class="comments-header p-30rpx border-bottom">
          <text class="text-32rpx font-bold">评论 ({{ comments.length }})</text>
        </view>

        <!-- 评论列表 -->
        <view v-if="comments.length > 0">
          <view
            v-for="(comment, index) in comments"
            :key="index"
            class="comment-item p-30rpx border-bottom"
          >
            <view class="flex">
              <image-loader :src="comment.avatar" class="commenter-avatar" />
              <view class="comment-content ml-20rpx flex-1">
                <view class="commenter-name text-28rpx font-medium">{{
                  comment.username
                }}</view>
                <view class="comment-text text-28rpx color-main mt-10rpx">{{
                  comment.content
                }}</view>
                <view class="comment-meta flex items-center mt-10rpx">
                  <text class="text-24rpx color-grey">{{
                    comment.createTime
                  }}</text>
                  <view
                    class="ml-30rpx flex items-center"
                    @tap="likeComment(index)"
                  >
                    <text
                      :class="
                        comment.isLiked
                          ? 'i-carbon-thumbs-up-filled text-primary'
                          : 'i-carbon-thumbs-up color-grey'
                      "
                      class="text-28rpx"
                    ></text>
                    <text
                      class="ml-6rpx text-24rpx"
                      :class="comment.isLiked ? 'text-primary' : 'color-grey'"
                    >
                      {{ comment.likes }}
                    </text>
                  </view>
                  <view
                    class="ml-30rpx flex items-center"
                    @tap="replyComment(comment)"
                  >
                    <text class="i-carbon-reply color-grey text-28rpx"></text>
                    <text class="ml-6rpx text-24rpx color-grey">回复</text>
                  </view>
                </view>

                <!-- 回复列表 -->
                <view
                  class="replies mt-20rpx"
                  v-if="comment.replies && comment.replies.length > 0"
                >
                  <view
                    v-for="(reply, replyIndex) in comment.replies"
                    :key="replyIndex"
                    class="reply-item bg-gray-50 p-20rpx rounded-lg mb-16rpx"
                  >
                    <view class="flex">
                      <image-loader
                        :src="reply.avatar"
                        class="replier-avatar"
                      />
                      <view class="reply-content ml-16rpx flex-1">
                        <view class="replier-name text-26rpx font-medium">
                          {{ reply.username }}
                          <text class="color-grey ml-10rpx">回复</text>
                          <text class="ml-10rpx">{{ reply.replyTo }}</text>
                        </view>
                        <view
                          class="reply-text text-26rpx color-main mt-6rpx"
                          >{{ reply.content }}</view
                        >
                        <view class="reply-meta flex items-center mt-6rpx">
                          <text class="text-22rpx color-grey">{{
                            reply.createTime
                          }}</text>
                          <view
                            class="ml-20rpx flex items-center"
                            @tap="likeReply(index, replyIndex)"
                          >
                            <text
                              :class="
                                reply.isLiked
                                  ? 'i-carbon-thumbs-up-filled text-primary'
                                  : 'i-carbon-thumbs-up color-grey'
                              "
                              class="text-24rpx"
                            ></text>
                            <text
                              class="ml-6rpx text-22rpx"
                              :class="
                                reply.isLiked ? 'text-primary' : 'color-grey'
                              "
                            >
                              {{ reply.likes }}
                            </text>
                          </view>
                          <view
                            class="ml-20rpx flex items-center"
                            @tap="replyComment(comment, reply.username)"
                          >
                            <text
                              class="i-carbon-reply color-grey text-24rpx"
                            ></text>
                            <text class="ml-6rpx text-22rpx color-grey"
                              >回复</text
                            >
                          </view>
                        </view>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 无评论时的提示 -->
        <view
          v-else
          class="no-comments p-40rpx flex flex-col items-center justify-center"
        >
          <text class="i-carbon-chat color-grey text-100rpx"></text>
          <text class="text-28rpx color-grey mt-20rpx"
            >暂无评论，快来抢沙发吧~</text
          >
        </view>
      </view>
    </scroll-view>

    <!-- 底部评论输入区域 -->
    <view
      class="comment-input-container"
      :style="{ transform: `translateY(-${keyboardHeight}px)` }"
    >
      <!-- 评论输入框 -->
      <view
        class="comment-input-wrapper flex items-center bg-white mx-20rpx p-20rpx rounded-lg"
      >
        <!-- 表情按钮 -->
        <view class="emoji-button mr-20rpx" @tap="toggleEmojiPanel">
          <text
            :class="
              showEmojiPanel
                ? 'i-carbon-keyboard color-primary'
                : 'i-carbon-face-satisfied color-grey'
            "
            class="text-44rpx"
          ></text>
        </view>

        <!-- 输入框 -->
        <textarea
          class="comment-input flex-1"
          v-model="commentText"
          :placeholder="replyTarget ? `回复 ${replyTarget}` : '添加评论...'"
          :adjust-position="false"
          :auto-height="true"
          :show-confirm-bar="false"
          :cursor-spacing="10"
          :focus="inputFocus"
          :hold-keyboard="holdKeyboard"
          :maxlength="500"
          @focus="onInputFocus"
          @blur="onInputBlur"
          @keyboardheightchange="onKeyboardHeightChange"
          @input="onInputChange"
        ></textarea>

        <!-- 发送按钮 -->
        <view
          class="send-btn ml-20rpx"
          :class="{ disabled: !commentText.trim() }"
          @tap="submitComment"
        >
          <text class="text-28rpx">发送</text>
        </view>
      </view>

      <!-- 表情面板 -->
      <view
        class="emoji-panel-container"
        :class="{ 'panel-show': showEmojiPanel }"
        :style="{ height: showEmojiPanel ? '400rpx' : '0' }"
      >
        <scroll-view scroll-y class="emoji-scroll">
          <view class="emoji-panel">
            <view
              v-for="(emoji, index) in emojiCodes"
              :key="index"
              class="emoji-item"
              @tap="insertEmoji(emoji)"
            >
              {{ emoji }}
            </view>
          </view>
        </scroll-view>

        <!-- 表情面板底部操作栏 -->
        <view
          class="emoji-footer flex items-center justify-between p-20rpx bg-gray-50"
        >
          <view
            class="emoji-delete-btn"
            :class="{ disabled: !commentText.trim() }"
            @tap="deleteLastCharacter"
          >
            <text class="i-carbon-backspace text-36rpx color-grey"></text>
          </view>

          <view
            class="emoji-send-btn"
            :class="{ disabled: !commentText.trim() }"
            @tap="submitComment"
          >
            <text class="text-28rpx">发送</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import PostItem from "@/components/PostItem.vue";
import ImageLoader from "@/components/ImageLoader.vue";
import { emojiCodes } from "@/constants/static/emoji";

// 获取页面参数
const postId = ref(0);

// 键盘和布局相关
const keyboardHeight = ref(0);
const baseContentHeight = ref(0);
const inputFocus = ref(false);
const holdKeyboard = ref(false);

// 表情面板相关
const showEmojiPanel = ref(false);

// 计算scroll-view高度
const scrollViewHeight = computed(() => {
  const basePx = baseContentHeight.value || 600; // 使用默认高度作为fallback
  const adjustedHeight = basePx - keyboardHeight.value;
  return `${adjustedHeight}px`;
});

// 动态详情数据
const postDetail = ref({
  id: 1,
  username: "张小明",
  avatar: "https://picsum.photos/seed/user2/100/100",
  position: "前端开发工程师",
  company: "优秀科技有限公司",
  content:
    "今天完成了一个重要项目的前端部分，使用了Vue3 + TypeScript，性能提升了30%。团队合作真的很重要，感谢每一位成员的付出！#前端开发 #Vue3",
  images: [
    "https://picsum.photos/seed/post1/400/300",
    "https://picsum.photos/seed/post2/400/300",
    "https://picsum.photos/seed/post3/400/300",
  ],
  likes: 45,
  comments: 12,
  isLiked: false,
  isFollowing: false,
  createTime: "10分钟前",
  location: "杭州市西湖区",
});

// 评论列表
const comments = ref([
  {
    id: 1,
    username: "李梦华",
    avatar: "https://picsum.photos/seed/user3/100/100",
    content: "这个项目确实很棒，我也参与了一部分开发，前端的优化思路很清晰！",
    createTime: "8分钟前",
    likes: 5,
    isLiked: false,
    replies: [
      {
        id: 101,
        username: "张小明",
        avatar: "https://picsum.photos/seed/user2/100/100",
        replyTo: "李梦华",
        content: "谢谢认可，你负责的组件交互做得也很棒！",
        createTime: "7分钟前",
        likes: 3,
        isLiked: true,
      },
      {
        id: 102,
        username: "王思聪",
        avatar: "https://picsum.photos/seed/user4/100/100",
        replyTo: "张小明",
        content: "你们的项目我也关注了，很有创新性，期待上线后的效果！",
        createTime: "5分钟前",
        likes: 2,
        isLiked: false,
      },
    ],
  },
  {
    id: 2,
    username: "赵丽颖",
    avatar: "https://picsum.photos/seed/user5/100/100",
    content: "作为UI设计师，很欣赏你们的界面实现，细节处理得很到位！",
    createTime: "6分钟前",
    likes: 8,
    isLiked: true,
    replies: [],
  },
  {
    id: 3,
    username: "王建国",
    avatar: "https://picsum.photos/seed/user4/100/100",
    content: "性能提升30%是怎么做到的？能分享一下具体的优化方案吗？",
    createTime: "4分钟前",
    likes: 3,
    isLiked: false,
    replies: [],
  },
]);

// 评论输入框内容
const commentText = ref("");

// 回复目标
const replyTarget = ref("");
const replyToComment = ref<any>(null);

// 初始化页面高度
onMounted(() => {
  try {
    // 使用UniApp API获取系统信息
    const systemInfo = uni.getSystemInfoSync();
    const windowHeight = systemInfo.windowHeight;

    // 计算基础高度：屏幕高度 - 导航栏高度 - 输入区域基础高度
    baseContentHeight.value = windowHeight - 44 - 100;
  } catch (error) {
    // 如果获取失败，使用默认值
    console.warn("Failed to get system info:", error);
    baseContentHeight.value = 600;
  }

  // 获取页面参数
  const eventChannel = getOpenerEventChannel();
  if (eventChannel) {
    eventChannel.on("acceptDataFromOpenerPage", function (data) {
      if (data && data.id) {
        postId.value = data.id;
      }
    });
  }
});

// 键盘高度变化处理
const onKeyboardHeightChange = (e: any) => {
  keyboardHeight.value = e.detail.height || 0;
};

// 输入框聚焦处理
const onInputFocus = () => {
  inputFocus.value = true;
  holdKeyboard.value = true;
  showEmojiPanel.value = false; // 聚焦时隐藏表情面板
};

// 输入框失焦处理
const onInputBlur = () => {
  inputFocus.value = false;
  // 延迟设置，防止表情面板切换时键盘闪烁
  setTimeout(() => {
    if (!showEmojiPanel.value) {
      holdKeyboard.value = false;
    }
  }, 100);
};

// 输入内容变化处理
const onInputChange = (e: any) => {
  commentText.value = e.detail.value;
};

// 切换表情面板
const toggleEmojiPanel = () => {
  showEmojiPanel.value = !showEmojiPanel.value;

  if (showEmojiPanel.value) {
    // 显示表情面板时，取消输入框聚焦但保持键盘高度
    inputFocus.value = false;
    holdKeyboard.value = true;
  } else {
    // 隐藏表情面板时，聚焦输入框
    inputFocus.value = true;
    holdKeyboard.value = true;
  }
};

// 插入表情
const insertEmoji = (emoji: string) => {
  commentText.value += emoji;
};

// 删除最后一个字符
const deleteLastCharacter = () => {
  if (commentText.value.length > 0) {
    commentText.value = commentText.value.slice(0, -1);
  }
};

// 点赞动态
const handleLike = () => {
  postDetail.value.isLiked = !postDetail.value.isLiked;
  postDetail.value.likes += postDetail.value.isLiked ? 1 : -1;
};

// 关注用户
const handleFollow = () => {
  postDetail.value.isFollowing = true;
  uni.showToast({
    title: "已关注",
    icon: "none",
  });
};

// 取消关注
const handleUnfollow = () => {
  postDetail.value.isFollowing = false;
  uni.showToast({
    title: "已取消关注",
    icon: "none",
  });
};

// 图片预览
const previewImage = (images: string[], current: number) => {
  uni.previewImage({
    urls: images,
    current: images[current],
  });
};

// 分享动态
const handleShare = () => {
  uni.showActionSheet({
    itemList: ["分享到微信", "分享到朋友圈", "复制链接", "分享到微博"],
    success: function (res) {
      uni.showToast({
        title: "分享功能开发中",
        icon: "none",
      });
    },
  });
};

// 打开评论输入框
const openCommentInput = () => {
  replyTarget.value = "";
  replyToComment.value = null;
  inputFocus.value = true;
  showEmojiPanel.value = false;
};

// 点赞评论
const likeComment = (index: number) => {
  const comment = comments.value[index];
  comment.isLiked = !comment.isLiked;
  comment.likes += comment.isLiked ? 1 : -1;
};

// 点赞回复
const likeReply = (commentIndex: number, replyIndex: number) => {
  const reply = comments.value[commentIndex].replies[replyIndex];
  reply.isLiked = !reply.isLiked;
  reply.likes += reply.isLiked ? 1 : -1;
};

// 回复评论
const replyComment = (comment: any, replyUsername?: string) => {
  replyTarget.value = replyUsername || comment.username;
  replyToComment.value = comment;
  inputFocus.value = true;
  showEmojiPanel.value = false;
};

// 提交评论
const submitComment = () => {
  if (!commentText.value.trim()) {
    uni.showToast({
      title: "评论内容不能为空",
      icon: "none",
    });
    return;
  }

  // 构建评论数据
  if (replyToComment.value) {
    // 回复评论
    const newReply = {
      id: Date.now(),
      username: "当前用户",
      avatar: "https://picsum.photos/seed/user1/100/100",
      replyTo: replyTarget.value,
      content: commentText.value,
      createTime: "刚刚",
      likes: 0,
      isLiked: false,
    };

    const index = comments.value.findIndex(
      (c) => c.id === replyToComment.value.id
    );
    if (index !== -1) {
      if (!comments.value[index].replies) {
        comments.value[index].replies = [];
      }
      comments.value[index].replies.push(newReply);
    }
  } else {
    // 新评论
    const newComment = {
      id: Date.now(),
      username: "当前用户",
      avatar: "https://picsum.photos/seed/user1/100/100",
      content: commentText.value,
      createTime: "刚刚",
      likes: 0,
      isLiked: false,
      replies: [],
    };

    comments.value.unshift(newComment);
    postDetail.value.comments += 1;
  }

  // 清空输入框和状态
  commentText.value = "";
  replyTarget.value = "";
  replyToComment.value = null;
  showEmojiPanel.value = false;
  inputFocus.value = false;
  holdKeyboard.value = false;

  uni.showToast({
    title: "评论成功",
    icon: "success",
  });
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 获取opener event channel
function getOpenerEventChannel() {
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const vm = currentPage.$vm;
  return vm?.$scope?.getOpenerEventChannel?.() || null;
}
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
}

.content-scroll {
  padding-top: 20rpx;
  transition: height 0.3s ease;
}

.border-bottom {
  border-bottom: 1rpx solid #f0f0f0;
}

.commenter-avatar {
  width: 70rpx;
  height: 70rpx;
  border-radius: 50%;
}

.replier-avatar {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
}

// 底部评论输入容器
.comment-input-container {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ffffff;
  border-top: 1rpx solid #f0f0f0;
  transition: transform 0.3s ease;
  z-index: 999;
}

// 评论输入框区域
.comment-input-wrapper {
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));

  .emoji-button {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10rpx;

    &:active {
      opacity: 0.7;
    }
  }

  .comment-input {
    min-height: 70rpx;
    max-height: 200rpx;
    background-color: #f5f5f5;
    border-radius: 35rpx;
    padding: 15rpx 30rpx;
    font-size: 28rpx;
    line-height: 1.4;
    border: none;
    resize: none;

    &:focus {
      background-color: #f0f0f0;
      outline: none;
    }
  }

  .send-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 100rpx;
    height: 60rpx;
    background: linear-gradient(135deg, $primary 0%, #4a90e2 100%);
    color: white;
    border-radius: 30rpx;
    font-size: 28rpx;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.3);

    &:active {
      transform: scale(0.95);
      box-shadow: 0 2rpx 8rpx rgba(74, 144, 226, 0.4);
    }

    &.disabled {
      background: #e0e0e0;
      color: #999;
      box-shadow: none;
      transform: none;

      &:active {
        transform: none;
      }
    }
  }
}

// 表情面板容器
.emoji-panel-container {
  overflow: hidden;
  transition: height 0.3s ease;
  background-color: #ffffff;
  border-top: 1rpx solid #f0f0f0;

  &.panel-show {
    border-top: 1rpx solid #e0e0e0;
  }

  .emoji-scroll {
    height: 320rpx;
  }

  .emoji-panel {
    display: flex;
    flex-wrap: wrap;
    padding: 20rpx;
    gap: 10rpx;

    .emoji-item {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 80rpx;
      height: 80rpx;
      font-size: 48rpx;
      border-radius: 12rpx;
      transition: all 0.2s ease;

      &:active {
        background-color: #f0f0f0;
        transform: scale(1.1);
      }
    }
  }

  .emoji-footer {
    height: 80rpx;
    border-top: 1rpx solid #f0f0f0;

    .emoji-delete-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 80rpx;
      height: 60rpx;
      border-radius: 12rpx;
      transition: all 0.2s ease;

      &:active:not(.disabled) {
        background-color: #f0f0f0;
      }

      &.disabled {
        opacity: 0.3;
      }
    }

    .emoji-send-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 120rpx;
      height: 60rpx;
      background: linear-gradient(135deg, $primary 0%, #4a90e2 100%);
      color: white;
      border-radius: 30rpx;
      font-size: 28rpx;
      font-weight: 500;
      transition: all 0.3s ease;

      &:active:not(.disabled) {
        transform: scale(0.95);
      }

      &.disabled {
        background: #e0e0e0;
        color: #999;
        transform: none;

        &:active {
          transform: none;
        }
      }
    }
  }
}

// 响应状态样式
.color-primary {
  color: $primary;
}

.text-primary {
  color: $primary;
}

.color-grey {
  color: #999;
}

.color-main {
  color: #333;
}

// 过渡动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

// 媒体查询适配不同屏幕
@media screen and (max-width: 750rpx) {
  .emoji-panel {
    .emoji-item {
      width: 70rpx;
      height: 70rpx;
      font-size: 42rpx;
    }
  }
}

// 暗色模式支持
@media (prefers-color-scheme: dark) {
  .comment-input-container {
    background-color: #1a1a1a;
    border-top-color: #333;
  }

  .comment-input {
    background-color: #2a2a2a;
    color: #fff;

    &:focus {
      background-color: #333;
    }
  }

  .emoji-panel-container {
    background-color: #1a1a1a;
    border-top-color: #333;

    .emoji-footer {
      background-color: #2a2a2a;
      border-top-color: #333;
    }
  }

  .emoji-item:active {
    background-color: #333 !important;
  }
}
</style>
