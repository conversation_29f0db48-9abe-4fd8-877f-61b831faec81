<template>
  <view class="container">
    <z-paging
      ref="paging"
      v-model="postList"
      @query="queryPosts"
      :show-scrollbar="false"
    >
    <template #top>
     <uni-nav-bar
      title="动态"
      :showBack="true"
      backgroundColor="#ffffff"
      :border="false"
      :fixed="true"
      statusBar="true"
    ></uni-nav-bar>
    </template>
      <!-- 动态内容列表 -->
      <template #default>
        <post-item
          v-for="(post, index) in postList"
          :key="index"
          :post="post"
          @like="toggleLike(index)"
          @comment="toggleComment(index)"
          @share="sharePost(index)"
          @follow="followUser(index)"
          @unfollow="unfollowUser(index)"
          @click="goToDetail(post)"
        />
      </template>

      <!-- 空数据占位 -->
      <template #empty>
        <view class="empty-view">
          <text class="i-carbon-document-blank color-grey text-100rpx"></text>
          <text class="empty-text">暂无动态内容</text>
          <text class="empty-hint">来发布第一条动态吧！</text>
        </view>
      </template>
    </z-paging>

    <!-- 发布动态入口浮动按钮 -->
    <view class="float-btn" @tap="goToPublish">
      <text class="i-carbon-add text-white text-50rpx"></text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import PostItem from "@/components/PostItem.vue";
import ImageLoader from "@/components/ImageLoader.vue";

// 分页组件引用
const paging = ref(null);

// 用户头像
const userAvatar = ref("");

// 动态列表数据
const postList = ref([]);

// 模拟数据请求
const queryPosts = (pageNo: number, pageSize: number) => {
  // 模拟网络请求
  setTimeout(() => {
    let data: any[] = [];

    if (pageNo === 1) {
      // 第一页数据
      data = [
        {
          id: 1,
          username: "张小明",
          avatar: "https://picsum.photos/seed/user2/100/100",
          position: "前端开发工程师",
          company: "优秀科技有限公司",
          content:
            "今天完成了一个重要项目的前端部分，使用了Vue3 + TypeScript，性能提升了30%。团队合作真的很重要，感谢每一位成员的付出！",
          topic: "#技术分享",
          images: [
            "https://picsum.photos/seed/post1/400/300",
          ],
          likes: 45,
          comments: 12,
          isLiked: false,
          isFollowing: false,
          createTime: "10分钟前",
          location: "杭州市西湖区",
        },
        {
          id: 2,
          username: "李梦华",
          avatar: "https://picsum.photos/seed/user3/100/100",
          position: "人力资源总监",
          company: "未来人才集团",
          content:
            "分享一个HR面试技巧：候选人的肢体语言有时比言语更能说明问题。观察眼神接触、坐姿和手势，能更全面地评估一个人。最近招聘了3位优秀的产品经理，团队越来越强大了！",
          images: [],
          video: "https://media.w3.org/2010/05/sintel/trailer.mp4",
          videoCover: "https://picsum.photos/seed/video1/400/300",
          likes: 67,
          comments: 23,
          isLiked: true,
          isFollowing: true,
          createTime: "30分钟前",
        },
        {
          id: 3,
          username: "王建国",
          avatar: "https://picsum.photos/seed/user4/100/100",
          position: "销售经理",
          company: "鼎盛商贸",
          content:
            "销售不仅是一种技能，更是一门艺术。今天成功签下了今年最大的一个客户，团队熬夜策划的方案终于获得认可。感谢团队的每一位成员，没有你们就没有今天的成功！",
          topic: "#销售精英",
          images: [
            "https://picsum.photos/seed/post4/400/300",
            "https://picsum.photos/seed/post14/400/300",
          ],
          likes: 89,
          comments: 34,
          isLiked: false,
          isFollowing: false,
          createTime: "2小时前",
          location: "上海市浦东新区",
        },
        {
          id: 4,
          username: "赵丽颖",
          avatar: "https://picsum.photos/seed/user5/100/100",
          position: "UI设计师",
          company: "创意设计工作室",
          content:
            "最近完成了一个电商App的UI设计，采用了新拟态设计风格，获得了客户的高度赞赏。设计灵感来源于自然界的光影效果，希望能给用户带来全新的视觉体验。",
          topic: "#UI设计",
          images: [
            "https://picsum.photos/seed/post5/400/300",
            "https://picsum.photos/seed/post6/400/300",
            "https://picsum.photos/seed/post7/400/300",
          ],
          likes: 125,
          comments: 45,
          isLiked: false,
          isFollowing: true,
          createTime: "昨天",
        },
      ];
    } else if (pageNo === 2) {
      // 第二页数据
      data = [
        {
          id: 5,
          username: "孙建明",
          avatar: "https://picsum.photos/seed/user6/100/100",
          position: "产品经理",
          company: "智能科技有限公司",
          content:
            "今天和开发团队进行了新产品的需求讨论，我们即将推出一款革命性的人工智能应用。这将是一次全新的尝试，希望能打破行业壁垒！",
          video: "https://media.w3.org/2010/05/sintel/trailer.mp4",
          videoCover: "https://picsum.photos/seed/video2/400/300",
          likes: 78,
          comments: 29,
          isLiked: true,
          isFollowing: false,
          createTime: "昨天",
          location: "北京市朝阳区",
        },
        {
          id: 6,
          username: "周星星",
          avatar: "https://picsum.photos/seed/user7/100/100",
          position: "电影导演",
          company: "喜剧之王影业",
          content: "一个好的剧本是电影成功的一半。我正在为我的下一部电影寻找灵感，希望能够再次为大家带来欢笑。",
          topic: "#电影人生",
          images: [
            "https://picsum.photos/seed/post8/400/300",
            "https://picsum.photos/seed/post9/400/300",
            "https://picsum.photos/seed/post10/400/300",
            "https://picsum.photos/seed/post11/400/300",
          ],
          likes: 256,
          comments: 88,
          isLiked: true,
          isFollowing: true,
          createTime: "前天",
          location: "香港",
        },
      ];
    } else if (pageNo === 3) {
      data = [
        {
          id: 7,
          username: "摄影爱好者",
          avatar: "https://picsum.photos/seed/user8/100/100",
          position: "自由摄影师",
          company: "光影瞬间",
          content: "分享一组最近拍摄的风光照片，大自然的美总是能治愈人心。",
          topic: "#摄影作品",
          images: [
            "https://picsum.photos/seed/img1/300/300",
            "https://picsum.photos/seed/img2/300/300",
            "https://picsum.photos/seed/img3/300/300",
            "https://picsum.photos/seed/img4/300/300",
            "https://picsum.photos/seed/img5/300/300",
            "https://picsum.photos/seed/img6/300/300",
            "https://picsum.photos/seed/img7/300/300",
            "https://picsum.photos/seed/img8/300/300",
            "https://picsum.photos/seed/img9/300/300",
          ],
          likes: 512,
          comments: 128,
          isLiked: false,
          isFollowing: false,
          createTime: "3天前",
          location: "桂林市阳朔县",
        },
        {
          id: 8,
          username: "美食家",
          avatar: "https://picsum.photos/seed/user9/100/100",
          position: "美食博主",
          company: "舌尖上的美味",
          content: "今天探店一家新开的日料，三文鱼非常新鲜，入口即化，强烈推荐！",
          topic: "#美食探店",
          images: [
            "https://picsum.photos/seed/food1/400/300",
            "https://picsum.photos/seed/food2/400/300",
            "https://picsum.photos/seed/food3/400/300",
            "https://picsum.photos/seed/food4/400/300",
            "https://picsum.photos/seed/food5/400/300",
          ],
          likes: 310,
          comments: 95,
          isLiked: true,
          isFollowing: true,
          createTime: "3天前",
          location: "广州市天河区",
        },
      ];
    }

    // 模拟加载完成，将数据传给z-paging
    if (paging.value) {
      paging.value.complete(data);
    }
  }, 1000);
};

// 点赞
const toggleLike = (index: number) => {
  const post = postList.value[index];
  post.isLiked = !post.isLiked;
  post.likes += post.isLiked ? 1 : -1;
};

// 关注用户
const followUser = (index: number) => {
  postList.value[index].isFollowing = true;
  uni.showToast({
    title: "已关注",
    icon: "none",
  });
};

// 取消关注
const unfollowUser = (index: number) => {
  postList.value[index].isFollowing = false;
  uni.showToast({
    title: "已取消关注",
    icon: "none",
  });
};

// 查看评论
const toggleComment = (index: number) => {
  uni.showToast({
    title: "查看评论功能开发中",
    icon: "none",
  });
};

// 分享
const sharePost = (index: number) => {
  uni.showToast({
    title: "分享功能开发中",
    icon: "none",
  });
};

// 跳转到发布动态页面
const goToPublish = () => {
  uni.navigateTo({
    url: "/pages/post/publish",
  });
};

// 跳转到帖子详情页面
const goToDetail = (post: any) => {
  uni.navigateTo({
    url: `/pages/post/detail?id=${post.id}`,
    success: (res) => {
      // 通过eventChannel向详情页传递数据
      res.eventChannel.emit("acceptDataFromOpenerPage", { id: post.id });
    },
  });
};

// 加载用户信息
onMounted(() => {
  // 模拟获取用户信息
  // 实际项目中应从服务器或本地缓存获取
  userAvatar.value = "https://picsum.photos/seed/user1/100/100";
});
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;

  .avatar {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
  }

  .publish-placeholder {
    height: 80rpx;
    line-height: 80rpx;
    border-radius: 40rpx;
    padding: 0 30rpx;
  }

  .empty-view {
    padding: 100rpx 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .empty-text {
    font-size: 32rpx;
    color: #999;
    margin-top: 30rpx;
    margin-bottom: 10rpx;
  }

  .empty-hint {
    font-size: 26rpx;
    color: #bbb;
  }

  .float-btn {
    position: fixed;
    right: 40rpx;
    bottom: 120rpx;
    width: 100rpx;
    height: 100rpx;
    border-radius: 50%;
    background-color: $primary;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 6rpx 16rpx rgba(255, 109, 0, 0.3);
    z-index: 99;
  }

  .text-primary {
    color: $primary;
  }
}
</style>
