<template>
  <view class="enterprise-verification-page">
    <uni-nav-bar
      :border="false"
      :fixed="true"
      status-bar="true"
      background-color="transparent"
      title=""
      left-icon="back"
      @clickLeft="handleBack"
    />
    <view class="page-container">
      <view class="header">
        <text class="title">企业认证</text>
        <text class="subtitle">认证企业身份，享受平台专属权益</text>
      </view>

      <!-- 类型选择器 -->
      <view class="type-selector">
        <view
          v-for="(item, index) in verificationTypes"
          :key="index"
          class="selector-item"
          :class="{ active: currentType === index }"
          @click="switchType(index)"
        >
          {{ item.name }}
        </view>
      </view>

      <!-- 上传营业执照 -->
      <view class="upload-prompt" @click="uploadLicense">
        <view class="prompt-icon-wrapper">
          <text class="i-carbon-document-import prompt-icon"></text>
        </view>
        <view class="prompt-text-wrapper">
          <text class="prompt-title">识别营业执照</text>
          <text class="prompt-subtitle">上传照片，自动填写下方信息</text>
        </view>
        <text class="i-carbon-chevron-right prompt-arrow"></text>
      </view>

      <!-- 表单区域 -->
      <view class="form-section">
        <!-- 个体户表单 -->
        <uni-forms
          v-if="currentType === 0"
          ref="solePropForm"
          :modelValue="solePropData"
          :rules="solePropRules"
        >
          <uni-forms-item name="operatorName">
            <view class="input-wrapper">
              <input
                v-model="solePropData.operatorName"
                class="form-input"
                placeholder="经营者姓名"
                :placeholder-style="placeholderStyle"
              />
            </view>
          </uni-forms-item>
          <uni-forms-item name="operatorIdCard">
            <view class="input-wrapper">
              <input
                v-model="solePropData.operatorIdCard"
                class="form-input"
                placeholder="经营者18位身份证号"
                :placeholder-style="placeholderStyle"
                maxlength="18"
              />
            </view>
          </uni-forms-item>
        </uni-forms>

        <!-- 企业单位表单 -->
        <uni-forms
          v-if="currentType === 1"
          ref="enterpriseForm"
          :modelValue="enterpriseData"
          :rules="enterpriseRules"
        >
          <uni-forms-item name="companyName">
            <view class="input-wrapper">
              <input
                v-model="enterpriseData.companyName"
                class="form-input"
                placeholder="企业名称"
                :placeholder-style="placeholderStyle"
              />
            </view>
          </uni-forms-item>
          <uni-forms-item name="creditCode">
            <view class="input-wrapper">
              <input
                v-model="enterpriseData.creditCode"
                class="form-input"
                placeholder="18位统一社会信用代码"
                :placeholder-style="placeholderStyle"
                maxlength="18"
              />
            </view>
          </uni-forms-item>
          <uni-forms-item name="legalPerson">
            <view class="input-wrapper">
              <input
                v-model="enterpriseData.legalPerson"
                class="form-input"
                placeholder="法人姓名"
                :placeholder-style="placeholderStyle"
              />
            </view>
          </uni-forms-item>
        </uni-forms>
      </view>

      <!-- 提交按钮 -->
      <view class="action-section">
        <button class="submit-button" @click="submitForm">
          同意协议并提交
        </button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from "vue";

const currentType = ref(0); // 0: 个体户, 1: 企业单位
const verificationTypes = [{ name: "个体工商户" }, { name: "企业单位" }];

const solePropForm = ref();
const enterpriseForm = ref();

const solePropData = ref({ operatorName: "", operatorIdCard: "" });
const enterpriseData = ref({
  companyName: "",
  creditCode: "",
  legalPerson: "",
});
const licenseImage = ref(""); // 仅用于判断是否上传，不再显示图片

const placeholderStyle = "color: var(--text-grey); font-size: 30rpx;";

const idCardRule = {
  pattern:
    /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
  errorMessage: "身份证号码格式不正确",
};
const creditCodeRule = {
  pattern: /^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/,
  errorMessage: "统一社会信用代码格式不正确",
};

const solePropRules = {
  operatorName: {
    rules: [{ required: true, errorMessage: "请输入经营者姓名" }],
  },
  operatorIdCard: {
    rules: [{ required: true, errorMessage: "请输入身份证号" }, idCardRule],
  },
};

const enterpriseRules = {
  companyName: { rules: [{ required: true, errorMessage: "请输入企业名称" }] },
  creditCode: {
    rules: [{ required: true, errorMessage: "请输入信用代码" }, creditCodeRule],
  },
  legalPerson: { rules: [{ required: true, errorMessage: "请输入法人姓名" }] },
};

const switchType = (index: number) => {
  currentType.value = index;
};

const uploadLicense = () => {
  uni.chooseImage({
    count: 1,
    sizeType: ["compressed"],
    sourceType: ["album", "camera"],
    success: (res) => {
      licenseImage.value = res.tempFilePaths[0];
      uni.showToast({
        title: "营业执照识别中...",
        icon: "loading",
        duration: 1500,
      });
      setTimeout(() => {
        if (currentType.value === 0) {
          solePropData.value.operatorName = "李四";
          solePropData.value.operatorIdCard = "******************";
        } else {
          enterpriseData.value.companyName = "示例科技有限公司";
          enterpriseData.value.creditCode = "91440101MA59A123BC";
          enterpriseData.value.legalPerson = "王五";
        }
        uni.showToast({ title: "识别成功", icon: "success" });
      }, 1500);
    },
  });
};

const submitForm = () => {
  const formRef =
    currentType.value === 0 ? solePropForm.value : enterpriseForm.value;
  formRef
    .validate()
    .then(() => {
      if (!licenseImage.value) {
        uni.showToast({ title: "请先上传并识别营业执照", icon: "none" });
        return;
      }
      uni.showLoading({ title: "提交中..." });
      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({ title: "提交成功，请等待审核", icon: "success" });
        uni.navigateBack();
      }, 1500);
    })
    .catch((err: any) => {
      console.log("表单错误信息：", err);
    });
};

const handleBack = () => {
  uni.navigateBack();
};
</script>

<style lang="scss" scoped>
.enterprise-verification-page {
  background-color: var(--bg-page);
  min-height: 100vh;
}
.page-container {
  padding: 32rpx;
}

.header {
  margin-bottom: 32rpx;
  .title {
    display: block;
    font-size: 44rpx;
    font-weight: 600;
    color: var(--text-base);
  }
  .subtitle {
    display: block;
    font-size: 28rpx;
    color: var(--text-info);
    margin-top: 16rpx;
  }
}

.type-selector {
  display: flex;
  background-color: var(--bg-input);
  border-radius: 48rpx;
  padding: 8rpx;
  margin: 48rpx 0;
  .selector-item {
    flex: 1;
    text-align: center;
    padding: 16rpx;
    font-size: 28rpx;
    color: var(--text-secondary);
    border-radius: 48rpx;
    transition: all 0.3s ease;
    &.active {
      background-color: var(--bg-card);
      color: var(--text-base);
      font-weight: 500;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
    }
  }
}

.upload-prompt {
  display: flex;
  align-items: center;
  background-color: var(--bg-primary-light);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 64rpx;

  .prompt-icon-wrapper {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    background-color: var(--primary);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 24rpx;

    .prompt-icon {
      font-size: 44rpx;
      color: var(--text-inverse);
    }
  }

  .prompt-text-wrapper {
    flex: 1;
    .prompt-title {
      display: block;
      font-size: 30rpx;
      font-weight: 500;
      color: var(--text-base);
    }
    .prompt-subtitle {
      display: block;
      font-size: 26rpx;
      color: var(--text-secondary);
      margin-top: 4rpx;
    }
  }

  .prompt-arrow {
    font-size: 32rpx;
    color: var(--text-grey);
  }
}

.form-section {
  :deep(.uni-forms-item) {
    margin-bottom: 24rpx;
  }

  .input-wrapper {
    width: 100%;
    padding: 24rpx 0;
    border-bottom: 1rpx solid var(--border-color);
    transition: border-color 0.3s;

    &:focus-within {
      border-color: var(--primary);
    }
  }

  .form-input {
    width: 100%;
    font-size: 32rpx;
    color: var(--text-base);
  }
}

.action-section {
  margin-top: 64rpx;
  .submit-button {
    background: linear-gradient(
      90deg,
      var(--primary-300),
      var(--primary-400),
      var(--primary)
    );
    color: #fff;
    border-radius: 48rpx;
    font-size: 32rpx;
    height: 96rpx;
    line-height: 96rpx;
    border: none;
    box-shadow: 0 8rpx 16rpx rgba(77, 148, 255, 0.2);
    &::after {
      border: none;
    }
  }
}
</style>
