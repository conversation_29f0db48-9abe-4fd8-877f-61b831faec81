<template>
  <view class="verification-center-page">
    <uni-nav-bar
      :border="false"
      :fixed="true"
      status-bar="true"
      background-color="transparent"
      title=""
      left-icon="back"
      @clickLeft="handleBack"
    />
    <!-- 弥散渐变背景 -->
    <view class="bg-gradient">
      <text class="i-carbon-shield-check bg-icon"></text>
    </view>

    <view class="page-content">
      <view class="header">
        <text class="title">认证中心</text>
        <text class="subtitle">提升账户信誉，解锁更多权益</text>
      </view>

      <!-- 认证卡片 -->
      <view class="verification-cards">
        <!-- 个人实名认证 -->
        <view class="card personal-card" @click="navigateToPersonal">
          <view class="card-content">
            <view class="card-icon-wrapper">
              <text class="i-carbon-user-identification card-icon"></text>
            </view>
            <view class="card-info">
              <text class="card-title">个人实名认证</text>
              <text class="card-description">通过公安系统验证证件真实性。</text>
            </view>
          </view>
          <view class="card-action">
            <template v-if="personalStatus === 1">
              <view class="status-badge verified">
                <text class="i-carbon-checkmark-outline badge-icon"></text>
                <text>已认证</text>
              </view>
            </template>
            <template v-else>
              <view class="action-button">
                <text>去认证</text>
              </view>
            </template>
          </view>
        </view>

        <!-- 企业认证 -->
        <view class="card enterprise-card" @click="navigateToEnterprise">
          <view class="card-content">
            <view class="card-icon-wrapper enterprise">
              <text class="i-carbon-building card-icon"></text>
            </view>
            <view class="card-info">
              <text class="card-title">企业认证</text>
              <text class="card-description">企业身份，尊享专属服务</text>
            </view>
          </view>
          <view class="card-action">
            <template v-if="enterpriseStatus === 1">
              <view class="status-badge verified">
                <text class="i-carbon-checkmark-outline badge-icon"></text>
                <text>已认证</text>
              </view>
            </template>
            <template v-else>
              <view class="action-button">
                <text>去认证</text>
              </view>
            </template>
          </view>
        </view>
      </view>

      <!-- 安全保障 -->
      <view class="security-assurance">
        <text class="i-carbon-shield security-icon"></text>
        <text class="security-text"
          >信息安全保障：我们采用加密技术，严格保护您的数据安全</text
        >
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { useUserStore } from "@/stores/user";

const userStore = useUserStore();
const userInfo = computed(() => userStore.getUser);

// 假设从用户信息中获取认证状态
const personalStatus = computed(
  () => userInfo.value?.verification?.personal_status
);
const enterpriseStatus = computed(
  () => userInfo.value?.verification?.enterprise_status
);

const navigateToPersonal = () => {
  // 如果已认证，可以考虑提示用户或禁用点击
  if (personalStatus.value === 1) {
    uni.showToast({ title: "您已完成个人认证", icon: "none" });
    return;
  }
  uni.navigateTo({
    url: "/pages/verification/personal",
  });
};

const navigateToEnterprise = () => {
  if (enterpriseStatus.value === 1) {
    uni.showToast({ title: "您已完成企业认证", icon: "none" });
    return;
  }
  uni.navigateTo({
    url: "/pages/verification/enterprise",
  });
};

const handleBack = () => {
  uni.navigateBack();
};
</script>

<style lang="scss" scoped>
.verification-center-page {
  position: relative;
  min-height: 100vh;
  background-color: var(--bg-page);
  overflow: hidden;
}

.bg-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 500rpx;
  background: linear-gradient(180deg, #eaf2ff 0%, var(--bg-page) 100%);
  z-index: 1;
  .bg-icon {
    position: absolute;
    top: 120rpx;
    right: -80rpx;
    font-size: 400rpx;
    color: rgba(255, 255, 255, 0.5);
    transform: rotate(-20deg);
  }
}

.page-content {
  position: relative;
  padding: 32rpx;
  z-index: 2;
}

.header {
  margin-top: 80rpx;
  margin-bottom: 48rpx;
  padding: 0 16rpx;
  .title {
    display: block;
    font-size: 52rpx;
    font-weight: 600;
    color: var(--text-base);
  }
  .subtitle {
    display: block;
    font-size: 28rpx;
    color: var(--text-info);
    margin-top: 16rpx;
  }
}

.verification-cards {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.card {
  padding: 32rpx;
  border-radius: 24rpx;
  background-color: var(--bg-card);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: space-between;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  }
}

.card-content {
  display: flex;
  align-items: center;
}

.card-icon-wrapper {
  width: 88rpx;
  height: 88rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  background: linear-gradient(135deg, #4d94ff, #62adff);

  &.enterprise {
    background: linear-gradient(135deg, #ff9500, #ffb34d);
  }

  .card-icon {
    font-size: 44rpx;
    color: var(--text-inverse);
  }
}

.card-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  .card-title {
    font-size: 34rpx;
    font-weight: 500;
  }
  .card-description {
    font-size: 26rpx;
    color: var(--text-info);
  }
}

.card-action {
  .action-button {
    display: flex;
    align-items: center;
    padding: 12rpx 20rpx;
    border-radius: 32rpx;
    background-color: var(--bg-page);
    font-size: 26rpx;
    font-weight: 500;
  }

  .status-badge {
    display: flex;
    align-items: center;
    font-size: 26rpx;

    .badge-icon {
      font-size: 32rpx;
      margin-right: 8rpx;
    }

    &.verified {
      color: var(--text-green);
    }
  }
}

.security-assurance {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 48rpx 0;
  margin-top: 64rpx;
  color: var(--text-info);
  font-size: 24rpx;

  .security-icon {
    font-size: 28rpx;
    margin-right: 12rpx;
    color: var(--text-grey);
  }
}
</style>
