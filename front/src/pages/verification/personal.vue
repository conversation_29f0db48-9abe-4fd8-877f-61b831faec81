<template>
  <view class="personal-verification-page">
    <uni-nav-bar
      :border="false"
      :fixed="true"
      status-bar="true"
      background-color="transparent"
      title=""
      left-icon="back"
      @clickLeft="handleBack"
    />
    <view class="page-container">
      <view class="header">
        <text class="title">个人实名认证</text>
        <text class="subtitle">信息仅用于身份核验，我们将严格保密</text>
      </view>

      <!-- 扫描识别按钮 -->
      <view class="scan-section" @click="handleScan">
        <view class="scan-content">
          <text class="i-carbon-qr-code scan-icon"></text>
          <text class="scan-text">扫描身份证，自动填写</text>
        </view>
        <text class="i-carbon-chevron-right scan-arrow"></text>
      </view>

      <view class="divider">
        <text class="divider-text">或手动填写</text>
      </view>

      <!-- 表单区域 -->
      <view class="form-section">
        <uni-forms ref="form" :modelValue="formData" :rules="rules">
          <uni-forms-item name="realName">
            <view class="input-wrapper">
              <input
                v-model="formData.realName"
                class="form-input"
                placeholder="请输入您的真实姓名"
                :placeholder-style="placeholderStyle"
              />
            </view>
          </uni-forms-item>
          <uni-forms-item name="idCard">
            <view class="input-wrapper">
              <input
                v-model="formData.idCard"
                class="form-input"
                placeholder="请输入您的18位身份证号码"
                :placeholder-style="placeholderStyle"
                maxlength="18"
              />
            </view>
          </uni-forms-item>
        </uni-forms>
      </view>

      <!-- 提交按钮 -->
      <view class="action-section">
        <button class="submit-button" @click="submitForm">
          同意协议并提交
        </button>
      </view>

      <view class="agreement-section">
        <text class="agreement-text">
          提交即代表您已阅读并同意
          <text class="link" @click="viewAgreement">《用户认证服务协议》</text>
        </text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from "vue";

const form = ref();
const formData = ref({
  realName: "",
  idCard: "",
});

const placeholderStyle = "color: var(--text-grey); font-size: 30rpx;";

const rules = {
  realName: {
    rules: [{ required: true, errorMessage: "请输入真实姓名" }],
  },
  idCard: {
    rules: [
      { required: true, errorMessage: "请输入身份证号" },
      {
        pattern:
          /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
        errorMessage: "身份证号码格式不正确",
      },
    ],
  },
};

const handleScan = () => {
  // 模拟OCR扫描识别
  uni.showToast({
    title: "OCR识别中...",
    icon: "loading",
    duration: 1500,
  });
  setTimeout(() => {
    formData.value.realName = "张三";
    formData.value.idCard = "******************";
    uni.showToast({
      title: "识别成功",
      icon: "success",
    });
  }, 1500);
};

const submitForm = () => {
  form.value
    .validate()
    .then(() => {
      uni.showLoading({ title: "提交中..." });

      // 模拟API提交
      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({
          title: "提交成功，请等待审核",
          icon: "success",
        });
        uni.navigateBack();
      }, 1500);
    })
    .catch((err: any) => {
      console.log("表单错误信息：", err);
    });
};

const viewAgreement = () => {
  // 跳转到协议页面
  uni.navigateTo({
    url: "/pages/public/agreement?type=verification",
  });
};

const handleBack = () => {
  uni.navigateBack();
};
</script>

<style lang="scss" scoped>
.personal-verification-page {
  background-color: var(--bg-page);
  min-height: 100vh;
}

.page-container {
  padding: 32rpx;
}

.header {
  margin-bottom: 64rpx;
  .title {
    display: block;
    font-size: 44rpx;
    font-weight: 600;
    color: var(--text-base);
  }
  .subtitle {
    display: block;
    font-size: 28rpx;
    color: var(--text-info);
    margin-top: 16rpx;
  }
}

.scan-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: var(--bg-card);
  padding: 32rpx;
  border-radius: 24rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.04);
  .scan-content {
    display: flex;
    align-items: center;
    color: var(--text-base);
  }
  .scan-icon {
    font-size: 48rpx;
    color: #4d94ff;
    margin-right: 24rpx;
  }
  .scan-text {
    font-size: 32rpx;
    font-weight: 500;
  }
  .scan-arrow {
    font-size: 32rpx;
    color: var(--text-grey);
  }
}

.divider {
  text-align: center;
  margin: 48rpx 0;
  color: var(--text-grey);
  font-size: 26rpx;
}

.form-section {
  :deep(.uni-forms-item) {
    margin-bottom: 24rpx;
  }
  .input-wrapper {
    width: 100%;
    padding: 24rpx 0;
    border-bottom: 1rpx solid var(--border-color);
    transition: border-color 0.3s;

    &:focus-within {
      border-color: #4d94ff;
    }
  }

  .form-input {
    width: 100%;
    font-size: 32rpx;
    color: var(--text-base);
  }
}

.action-section {
  margin-top: 64rpx;
}

.submit-button {
  background: linear-gradient(90deg, #4d94ff, #62adff);
  color: var(--text-inverse);
  border-radius: 48rpx;
  font-size: 32rpx;
  height: 96rpx;
  line-height: 96rpx;
  border: none;
  box-shadow: 0 8rpx 16rpx rgba(77, 148, 255, 0.2);

  &::after {
    border: none;
  }
}

.agreement-section {
  margin-top: 32rpx;
  text-align: center;
  .agreement-text {
    color: var(--text-info);
    font-size: 24rpx;
  }
  .link {
    color: #4d94ff;
  }
}
</style>
