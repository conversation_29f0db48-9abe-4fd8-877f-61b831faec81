/**
 * 服务基类
 * 提供统一的错误处理、日志记录、状态管理等基础功能
 */

import { useUserStore } from '@/stores/user'
import { useGlobalStore } from '@/stores/global'
import { showErrorToast, showLoading, hideLoading } from '@/utils/ui/feedback'

export interface ServiceResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  code?: number
}

export interface ServiceError {
  message: string
  code?: number
  details?: any
}

export interface ServiceOptions {
  showLoading?: boolean
  loadingText?: string
  showError?: boolean
  throwOnError?: boolean
}

/**
 * 服务基类
 * 所有业务服务都应继承此类
 */
export abstract class BaseService {
  protected userStore = useUserStore()
  protected globalStore = useGlobalStore()

  /**
   * 服务名称，用于日志记录
   */
  protected abstract serviceName: string

  /**
   * 记录日志
   * @param level 日志级别
   * @param message 日志消息
   * @param data 附加数据
   */
  protected log(level: 'info' | 'warn' | 'error', message: string, data?: any) {
    const timestamp = new Date().toISOString()
    const logMessage = `[${timestamp}] [${this.serviceName}] ${message}`
    
    switch (level) {
      case 'info':
        console.info(logMessage, data)
        break
      case 'warn':
        console.warn(logMessage, data)
        break
      case 'error':
        console.error(logMessage, data)
        break
    }
  }

  /**
   * 创建服务错误
   * @param message 错误消息
   * @param code 错误代码
   * @param details 错误详情
   */
  protected createError(message: string, code?: number, details?: any): ServiceError {
    return {
      message,
      code,
      details
    }
  }

  /**
   * 处理异步操作
   * @param operation 异步操作函数
   * @param options 选项配置
   */
  protected async handleOperation<T>(
    operation: () => Promise<T>,
    options: ServiceOptions = {}
  ): Promise<ServiceResponse<T>> {
    const {
      showLoading: shouldShowLoading = false,
      loadingText = '加载中...',
      showError = true,
      throwOnError = false
    } = options

    try {
      // 显示加载状态
      if (shouldShowLoading) {
        showLoading(loadingText)
      }

      // 执行操作
      const result = await operation()

      this.log('info', '操作执行成功')
      
      return {
        success: true,
        data: result
      }
    } catch (error: any) {
      const errorMessage = this.extractErrorMessage(error)
      
      this.log('error', '操作执行失败', { error: errorMessage, details: error })

      // 显示错误提示
      if (showError) {
        showErrorToast(errorMessage)
      }

      const serviceResponse: ServiceResponse<T> = {
        success: false,
        error: errorMessage,
        code: error.code || error.statusCode
      }

      // 根据配置决定是否抛出错误
      if (throwOnError) {
        throw this.createError(errorMessage, error.code, error)
      }

      return serviceResponse
    } finally {
      // 隐藏加载状态
      if (shouldShowLoading) {
        hideLoading()
      }
    }
  }

  /**
   * 提取错误消息
   * @param error 错误对象
   */
  private extractErrorMessage(error: any): string {
    if (typeof error === 'string') {
      return error
    }
    
    if (error.message) {
      return error.message
    }
    
    if (error.errMsg) {
      return error.errMsg
    }
    
    return '操作失败，请重试'
  }

  /**
   * 检查用户登录状态
   * @throws {ServiceError} 当用户未登录时抛出错误
   */
  protected requireLogin(): void {
    if (!this.userStore.isLogin) {
      throw this.createError('请先登录', 401)
    }
  }

  /**
   * 检查用户VIP状态
   * @throws {ServiceError} 当用户不是VIP时抛出错误
   */
  protected requireVip(): void {
    this.requireLogin()
    
    if (!this.userStore.user?.isVip) {
      throw this.createError('此功能仅限VIP用户使用', 403)
    }
  }

  /**
   * 安全执行需要登录的操作
   * @param operation 需要登录的操作
   * @param options 选项配置
   */
  protected async executeWithLogin<T>(
    operation: () => Promise<T>,
    options: ServiceOptions = {}
  ): Promise<ServiceResponse<T>> {
    return this.handleOperation(
      async () => {
        this.requireLogin()
        return await operation()
      },
      options
    )
  }

  /**
   * 安全执行需要VIP的操作
   * @param operation 需要VIP的操作
   * @param options 选项配置
   */
  protected async executeWithVip<T>(
    operation: () => Promise<T>,
    options: ServiceOptions = {}
  ): Promise<ServiceResponse<T>> {
    return this.handleOperation(
      async () => {
        this.requireVip()
        return await operation()
      },
      options
    )
  }

  /**
   * 批量处理操作
   * @param operations 操作列表
   * @param options 选项配置
   */
  protected async handleBatchOperations<T>(
    operations: (() => Promise<T>)[],
    options: ServiceOptions = {}
  ): Promise<ServiceResponse<T[]>> {
    return this.handleOperation(
      async () => {
        const results = await Promise.allSettled(operations.map(op => op()))
        
        const successResults: T[] = []
        const errors: any[] = []
        
        results.forEach((result, index) => {
          if (result.status === 'fulfilled') {
            successResults.push(result.value)
          } else {
            errors.push({
              index,
              error: result.reason
            })
          }
        })
        
        if (errors.length > 0) {
          this.log('warn', `批量操作部分失败，成功: ${successResults.length}, 失败: ${errors.length}`, errors)
        }
        
        return successResults
      },
      options
    )
  }

  /**
   * 重试机制包装器
   * @param operation 要重试的操作
   * @param maxRetries 最大重试次数
   * @param delay 重试延迟（毫秒）
   */
  protected async withRetry<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    delay: number = 1000
  ): Promise<T> {
    let lastError: any
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation()
      } catch (error) {
        lastError = error
        
        if (attempt === maxRetries) {
          this.log('error', `操作在 ${maxRetries} 次重试后仍然失败`, error)
          throw error
        }
        
        this.log('warn', `操作失败，第 ${attempt} 次重试`, error)
        
        // 等待指定延迟后重试
        await new Promise(resolve => setTimeout(resolve, delay))
      }
    }
    
    throw lastError
  }

  /**
   * 缓存包装器
   * @param key 缓存键
   * @param operation 获取数据的操作
   * @param ttl 缓存时间（毫秒）
   */
  protected async withCache<T>(
    key: string,
    operation: () => Promise<T>,
    ttl: number = 5 * 60 * 1000 // 默认5分钟
  ): Promise<T> {
    const cacheKey = `${this.serviceName}:${key}`
    
    try {
      // 尝试从缓存获取
      const cached = uni.getStorageSync(cacheKey)
      if (cached && cached.expireTime > Date.now()) {
        this.log('info', `从缓存获取数据: ${key}`)
        return cached.data
      }
    } catch (error) {
      this.log('warn', '读取缓存失败', error)
    }
    
    // 缓存未命中，执行操作
    const result = await operation()
    
    try {
      // 将结果存入缓存
      uni.setStorageSync(cacheKey, {
        data: result,
        expireTime: Date.now() + ttl
      })
      this.log('info', `数据已缓存: ${key}`)
    } catch (error) {
      this.log('warn', '写入缓存失败', error)
    }
    
    return result
  }

  /**
   * 清除服务相关的所有缓存
   */
  protected clearCache(): void {
    try {
      const info = uni.getStorageInfoSync()
      const keys = info.keys.filter(key => key.startsWith(`${this.serviceName}:`))
      
      keys.forEach(key => {
        uni.removeStorageSync(key)
      })
      
      this.log('info', `已清除 ${keys.length} 个缓存项`)
    } catch (error) {
      this.log('warn', '清除缓存失败', error)
    }
  }
}

/**
 * 创建服务响应
 * @param success 是否成功
 * @param data 数据
 * @param error 错误信息
 * @param code 错误代码
 */
export function createServiceResponse<T>(
  success: boolean,
  data?: T,
  error?: string,
  code?: number
): ServiceResponse<T> {
  return {
    success,
    data,
    error,
    code
  }
}

/**
 * 服务工厂函数
 * 用于创建服务实例的通用方法
 */
export function createService<T extends BaseService>(
  ServiceClass: new (...args: any[]) => T,
  ...args: any[]
): T {
  return new ServiceClass(...args)
}