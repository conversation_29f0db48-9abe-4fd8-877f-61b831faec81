/**
 * 零工申请相关服务
 */
import { ref } from 'vue'
import { useRequest } from 'alova/client'
import GigApi from '@/api/gig'
import type { GigApplication, ApplyGigRequest, UpdateApplicationStatusRequest } from '@/types/gig'
import { showToast, showSuccessToast } from '@/utils/ui/feedback'
import { useUserStore } from '@/stores/user'

/**
 * 申请零工的业务逻辑
 */
export function useGigApplication() {
  const userStore = useUserStore()

  // 申请零工
  const {
    loading: isApplying,
    error: applyError,
    send: applyForGig
  } = useRequest(
    (request: ApplyGigRequest) => GigApi.apply(request),
    { immediate: false }
  ).onSuccess(() => {
    showSuccessToast('申请提交成功，请等待雇主确认')
  }).onError((error) => {
    console.error('申请零工失败:', error)
    showToast('申请失败，请重试')
  })

  // 撤销申请
  const {
    loading: isWithdrawing,
    error: withdrawError,
    send: withdrawApplication
  } = useRequest(
    (applicationId: number) => GigApi.deleteApplication(applicationId),
    { immediate: false }
  ).onSuccess(() => {
    showSuccessToast('申请已撤销')
  }).onError((error) => {
    console.error('撤销申请失败:', error)
    showToast('撤销失败，请重试')
  })

  // 更新申请状态（雇主操作）
  const {
    loading: isUpdatingStatus,
    error: updateStatusError,
    send: updateApplicationStatus
  } = useRequest(
    (request: UpdateApplicationStatusRequest) => GigApi.updateApplicationStatus(request),
    { immediate: false }
  ).onSuccess(() => {
    showSuccessToast('操作成功')
  }).onError((error) => {
    console.error('更新申请状态失败:', error)
    showToast('操作失败，请重试')
  })

  return {
    // 申请相关
    isApplying,
    applyError,
    applyForGig,

    // 撤销相关
    isWithdrawing,
    withdrawError,
    withdrawApplication,

    // 状态更新相关（雇主用）
    isUpdatingStatus,
    updateStatusError,
    updateApplicationStatus
  }
}

/**
 * 获取用户的申请列表
 */
export function useMyApplications() {
  const {
    data: applications,
    loading: isLoading,
    error: fetchError,
    send: refresh
  } = useRequest(
    () => GigApi.getMyApplications(),
    { immediate: true }
  ).onError((error) => {
    console.error('获取申请列表失败:', error)
  })

  return {
    applications: applications ?? ref([]),
    isLoading,
    fetchError,
    refresh
  }
}

/**
 * 获取零工的申请列表（雇主用）
 */
export function useGigApplications(gigId: number) {
  const {
    data: applications,
    loading: isLoading,
    error: fetchError,
    send: refresh
  } = useRequest(
    () => GigApi.getGigApplications(gigId),
    { immediate: true }
  ).onError((error) => {
    console.error('获取零工申请列表失败:', error)
  })

  return {
    applications: applications ?? ref([]),
    isLoading,
    fetchError,
    refresh
  }
}

/**
 * 检查用户对特定零工的申请状态
 */
export function checkAppStatus(gigId: number) {
  const userStore = useUserStore()

  const {
    data: application,
    loading: isLoading,
    error: fetchError,
    send: checkStatus
  } = useRequest(
    () => GigApi.checkApplicationStatus(gigId),
    {
      immediate: !!userStore.isLogin,
      // 禁用缓存，确保每次都获取最新数据
      cacheFor: 0,
      // 如果用户未登录，返回null
      transformer: (data) => userStore.isLogin ? data : null
    }
  ).onError((error) => {
    // 静默处理错误，404表示用户未申请是正常情况
    console.error('检查申请状态失败:', error)
    showToast('获取报名状态失败，请重试')
  })

  return {
    application: application ?? ref(null),
    isLoading,
    fetchError,
    checkStatus
  }
}

/**
 * 申请表单验证逻辑
 */
export function useApplicationFormValidation() {
  /**
   * 验证联系人姓名
   */
  const validateContactName = (name: string): string | null => {
    if (!name || name.trim().length === 0) {
      return '请输入您的姓名'
    }

    if (name.trim().length < 2) {
      return '姓名至少需要2个字符'
    }

    if (name.trim().length > 20) {
      return '姓名不能超过20个字符'
    }

    // 检查是否包含特殊字符（保留中文、英文、数字）
    const nameRegex = /^[\u4E00-\u9FA5A-Za-z0-9\s]+$/
    if (!nameRegex.test(name.trim())) {
      return '姓名只能包含中文、英文和数字'
    }

    return null
  }

  /**
   * 验证手机号码
   */
  const validateContactPhone = (phone: string): string | null => {
    if (!phone || phone.trim().length === 0) {
      return '请输入手机号码'
    }

    // 中国大陆手机号码正则
    const phoneRegex = /^1[3-9]\d{9}$/
    if (!phoneRegex.test(phone.trim())) {
      return '请输入正确的手机号码'
    }

    return null
  }

  /**
   * 验证经验描述（备注）
   */
  const validateRemarks = (remarks: string): string | null => {
    if (remarks && remarks.trim().length > 200) {
      return '备注信息不能超过200个字符'
    }

    return null
  }


  /**
   * 验证整个申请表单
   */
  const validateApplicationForm = (formData: any): Record<string, string> => {
    const errors: Record<string, string> = {}

    const nameError = validateContactName(formData.contactName)
    if (nameError) errors.contactName = nameError

    const phoneError = validateContactPhone(formData.contactPhone)
    if (phoneError) errors.contactPhone = phoneError

    const remarksError = validateRemarks(formData.remarks)
    if (remarksError) errors.remarks = remarksError

    if (!formData.agreedToTerms) {
      errors.agreement = '请同意申请服务协议'
    }

    return errors
  }

  return {
    validateContactName,
    validateContactPhone,
    validateRemarks,
    validateApplicationForm
  }
}

/**
 * 申请表单数据处理
 */
export function useApplicationFormData() {
  /**
   * 将表单数据转换为API请求格式
   */
  const transformFormDataToRequest = (
    gigId: number,
    formData: any
  ): ApplyGigRequest => {
    return {
      gig_id: gigId,
      contact_name: formData.contactName.trim(),
      contact_phone: formData.contactPhone.trim(),
      has_experience: formData.hasExperience,
      experience_description: formData.remarks?.trim() || '',
      message: formData.remarks?.trim() || ''
    }
  }

  /**
   * 获取经验等级的显示文本
   */
  const getExperienceText = (hasExperience: boolean): string => {
    return hasExperience ? '有相关经验' : '无相关经验'
  }

  return {
    transformFormDataToRequest,
    getExperienceText
  }
}