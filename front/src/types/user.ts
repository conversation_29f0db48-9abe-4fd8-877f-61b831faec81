// src/types/user.ts

/**
 * 用户信息
 */
export interface UserInfo {
    id: number;
    uid: string;
    nickname: string;
    avatar: string;
    gender: number;
    birthday: string;
    phone: string;
    email: string;
    isVip?: boolean;
    vipExpiresAt: string;
    isSuperAdmin: boolean;
    posts: number;
    followers: number;
    following: number;
    balance: number;
    createdAt: string;
    updatedAt: string;
    personalVerified: boolean;
    enterpriseVerified: boolean;
    memberLevel: number;
} 