/**
 * 日期时间工具函数
 * 提供统一的时间格式化和处理功能
 */
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import isTodayPlugin from 'dayjs/plugin/isToday';
import isYesterdayPlugin from 'dayjs/plugin/isYesterday';
import isTomorrowPlugin from 'dayjs/plugin/isTomorrow';
import 'dayjs/locale/zh-cn';

// 设置中文语言
dayjs.locale('zh-cn');

// 加载插件
dayjs.extend(relativeTime);
dayjs.extend(isSameOrBefore);
dayjs.extend(isSameOrAfter);
dayjs.extend(isTodayPlugin);
dayjs.extend(isYesterdayPlugin);
dayjs.extend(isTomorrowPlugin);

// ====================================================================
// 日期格式类型定义
// ====================================================================

export enum DateFormat {
  // 日期格式
  DATE_SIMPLE = 'YYYY-MM-DD',
  DATE_CHINESE = 'MM月DD日',
  DATE_FULL_CHINESE = 'YYYY年MM月DD日',
  
  // 时间格式
  TIME_SIMPLE = 'HH:mm',
  TIME_WITH_SECONDS = 'HH:mm:ss',
  
  // 日期时间格式
  DATETIME_SIMPLE = 'YYYY-MM-DD HH:mm',
  DATETIME_CHINESE = 'MM月DD日 HH:mm',
  DATETIME_FULL_CHINESE = 'YYYY年MM月DD日 HH:mm',
  DATETIME_WITH_SECONDS = 'YYYY-MM-DD HH:mm:ss'
}

export enum RelativeTimeMode {
  SMART = 'smart',       // 智能显示：今天、昨天、明天等
  PUBLISH = 'publish',   // 发布时间：刚刚、X分钟前等
  PRECISE = 'precise'    // 精确时间：直接显示时间
}

// ====================================================================
// 统一格式化函数
// ====================================================================

/**
 * 统一日期格式化函数
 * @param date 日期
 * @param format 格式类型
 */
export function formatDate(date: string | Date | dayjs.Dayjs, format: DateFormat = DateFormat.DATE_SIMPLE): string {
  return dayjs(date).format(format);
}


// ====================================================================
// 统一相对时间函数
// ====================================================================

/**
 * 统一相对时间格式化函数
 * @param date 日期
 * @param mode 显示模式
 */
export function formatRelativeTime(date: string | Date | dayjs.Dayjs, mode: RelativeTimeMode = RelativeTimeMode.SMART): string {
  const target = dayjs(date);
  const now = dayjs();

  switch (mode) {
    case RelativeTimeMode.PUBLISH:
      return formatPublishTimeInternal(target, now);
    
    case RelativeTimeMode.PRECISE:
      return target.format(DateFormat.DATETIME_CHINESE);
    
    case RelativeTimeMode.SMART:
    default:
      return formatSmartTimeInternal(target, now);
  }
}

/**
 * 内部函数：智能时间格式化
 */
function formatSmartTimeInternal(target: dayjs.Dayjs, now: dayjs.Dayjs): string {
  if (target.isToday()) {
    const diffMinutes = now.diff(target, 'minute');
    if (diffMinutes < 1) {
      return '刚刚';
    } else if (diffMinutes < 60) {
      return `${diffMinutes}分钟前`;
    } else {
      return `今天 ${target.format('HH:mm')}`;
    }
  }

  if (target.isYesterday()) {
    return `昨天 ${target.format('HH:mm')}`;
  }

  if (target.isTomorrow()) {
    return `明天 ${target.format('HH:mm')}`;
  }

  if (target.isSame(now, 'year')) {
    return target.format('MM月DD日 HH:mm');
  }

  return target.format('YYYY年MM月DD日 HH:mm');
}

/**
 * 内部函数：发布时间格式化
 */
function formatPublishTimeInternal(target: dayjs.Dayjs, now: dayjs.Dayjs): string {
  const diffMinutes = now.diff(target, 'minute');
  const diffHours = now.diff(target, 'hour');
  const diffDays = now.diff(target, 'day');

  if (diffMinutes < 1) {
    return '刚刚发布';
  } else if (diffMinutes < 60) {
    return `${diffMinutes}分钟前`;
  } else if (diffHours < 24) {
    return `${diffHours}小时前`;
  } else if (diffDays < 7) {
    return `${diffDays}天前`;
  } else {
    return target.format('MM月DD日');
  }
}


// ====================================================================
// 日期判断函数
// ====================================================================

/**
 * 检查日期是否是今天
 * @param date 日期
 */
export function isToday(date: string | Date | dayjs.Dayjs): boolean {
  return dayjs(date).isToday();
}

/**
 * 检查日期是否是昨天
 * @param date 日期
 */
export function isYesterday(date: string | Date | dayjs.Dayjs): boolean {
  return dayjs(date).isYesterday();
}

/**
 * 检查日期是否是明天
 * @param date 日期
 */
export function isTomorrow(date: string | Date | dayjs.Dayjs): boolean {
  return dayjs(date).isTomorrow();
}

/**
 * 检查日期是否是过去的
 * @param date 日期
 */
export function isPast(date: string | Date | dayjs.Dayjs): boolean {
  return dayjs(date).isBefore(dayjs());
}

/**
 * 检查日期是否是未来的
 * @param date 日期
 */
export function isFuture(date: string | Date | dayjs.Dayjs): boolean {
  return dayjs(date).isAfter(dayjs());
}

/**
 * 检查日期是否在指定范围内
 * @param date 要检查的日期
 * @param startDate 开始日期
 * @param endDate 结束日期
 */
export function isInRange(
  date: string | Date | dayjs.Dayjs,
  startDate: string | Date | dayjs.Dayjs,
  endDate: string | Date | dayjs.Dayjs
): boolean {
  const target = dayjs(date);
  return target.isSameOrAfter(dayjs(startDate)) && target.isSameOrBefore(dayjs(endDate));
}

// ====================================================================
// 时间计算函数
// ====================================================================

/**
 * 计算两个日期之间的差值
 * @param startDate 开始日期
 * @param endDate 结束日期
 * @param unit 单位（'day', 'hour', 'minute' 等）
 */
export function getDiff(
  startDate: string | Date | dayjs.Dayjs,
  endDate: string | Date | dayjs.Dayjs,
  unit: dayjs.ManipulateType = 'day'
): number {
  return dayjs(endDate).diff(dayjs(startDate), unit);
}

/**
 * 计算并格式化工作时长（核心功能）
 * @param startTime 开始时间
 * @param endTime 结束时间
 */
export function formatWorkDuration(
  startTime: string | Date | dayjs.Dayjs,
  endTime: string | Date | dayjs.Dayjs
): string {
  const hours = dayjs(endTime).diff(dayjs(startTime), 'hour');

  if (hours < 24) {
    return `${hours}小时`;
  }
  
  const days = Math.floor(hours / 24);
  const remainingHours = hours % 24;
  
  return remainingHours === 0 ? `${days}天` : `${days}天${remainingHours}小时`;
}

/**
 * 添加指定时间
 * @param date 基准日期
 * @param value 数值
 * @param unit 单位
 */
export function addTime(
  date: string | Date | dayjs.Dayjs,
  value: number,
  unit: dayjs.ManipulateType
): dayjs.Dayjs {
  return dayjs(date).add(value, unit);
}

/**
 * 减去指定时间
 * @param date 基准日期
 * @param value 数值
 * @param unit 单位
 */
export function subtractTime(
  date: string | Date | dayjs.Dayjs,
  value: number,
  unit: dayjs.ManipulateType
): dayjs.Dayjs {
  return dayjs(date).subtract(value, unit);
}

// ====================================================================
// 特殊日期函数
// ====================================================================

/**
 * 获取今天的开始时间（00:00:00）
 */
export function getStartOfToday(): dayjs.Dayjs {
  return dayjs().startOf('day');
}

/**
 * 获取今天的结束时间（23:59:59）
 */
export function getEndOfToday(): dayjs.Dayjs {
  return dayjs().endOf('day');
}

/**
 * 获取指定日期的开始时间
 * @param date 日期
 */
export function getStartOfDay(date: string | Date | dayjs.Dayjs): dayjs.Dayjs {
  return dayjs(date).startOf('day');
}

/**
 * 获取指定日期的结束时间
 * @param date 日期
 */
export function getEndOfDay(date: string | Date | dayjs.Dayjs): dayjs.Dayjs {
  return dayjs(date).endOf('day');
}

/**
 * 获取本周的开始时间（周一）
 */
export function getStartOfWeek(): dayjs.Dayjs {
  return dayjs().startOf('week');
}

/**
 * 获取本周的结束时间（周日）
 */
export function getEndOfWeek(): dayjs.Dayjs {
  return dayjs().endOf('week');
}

/**
 * 获取本月的开始时间
 */
export function getStartOfMonth(): dayjs.Dayjs {
  return dayjs().startOf('month');
}

/**
 * 获取本月的结束时间
 */
export function getEndOfMonth(): dayjs.Dayjs {
  return dayjs().endOf('month');
}

// ====================================================================
// 时间验证函数
// ====================================================================

/**
 * 验证日期字符串是否有效
 * @param dateString 日期字符串
 */
export function isValidDate(dateString: string): boolean {
  return dayjs(dateString).isValid();
}

/**
 * 验证时间格式是否正确（HH:mm）
 * @param timeString 时间字符串
 */
export function isValidTime(timeString: string): boolean {
  return /^([01]\d|2[0-3]):([0-5]\d)$/.test(timeString);
}

/**
 * 验证日期时间格式是否正确（YYYY-MM-DD HH:mm）
 * @param dateTimeString 日期时间字符串
 */
export function isValidDateTime(dateTimeString: string): boolean {
  return /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}$/.test(dateTimeString) &&
    dayjs(dateTimeString, 'YYYY-MM-DD HH:mm').isValid();
}

// ====================================================================
// 日期展示优化函数
// ====================================================================

/**
 * 智能日期显示（仅日期，不含时间）
 * @param date 日期
 * @param showYear 是否强制显示年份
 */
export function formatDateSmart(date: string | Date | dayjs.Dayjs, showYear: boolean = false): string {
  const target = dayjs(date);
  const now = dayjs();

  if (target.isToday()) {
    return '今天';
  }

  if (target.isYesterday()) {
    return '昨天';
  }

  if (target.isTomorrow()) {
    return '明天';
  }

  if (target.isSame(now, 'year') && !showYear) {
    return target.format('MM月DD日');
  }

  return target.format('YYYY年MM月DD日');
}

/**
 * 格式化时间段显示（核心功能）
 * @param startTime 开始时间
 * @param endTime 结束时间
 * @param showSeconds 是否显示秒
 */
export function formatTimeRange(
  startTime: string | Date | dayjs.Dayjs,
  endTime: string | Date | dayjs.Dayjs,
  showSeconds: boolean = false
): string {
  if (!startTime || !endTime) return '时间待定';

  const start = dayjs(startTime);
  const end = dayjs(endTime);
  const format = showSeconds ? 'HH:mm:ss' : 'HH:mm';

  if (start.isSame(end, 'day')) {
    return `${start.format('MM月DD日')} ${start.format(format)}-${end.format(format)}`;
  }
  
  return `${start.format(`MM月DD日 ${format}`)} - ${end.format(`MM月DD日 ${format}`)}`;
}

// ====================================================================
// 导出 dayjs 实例供高级使用
// ====================================================================

export { dayjs };