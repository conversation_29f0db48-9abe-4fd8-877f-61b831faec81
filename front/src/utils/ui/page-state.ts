/**
 * 页面状态管理工具
 * 用于PageLayout组件的智能状态管理
 */

import { ref, computed, watch, type Ref } from 'vue'
import { showErrorToast } from './feedback'

// ====================================================================
// 页面状态枚举
// ====================================================================

/**
 * 页面状态枚举
 */
export enum PageState {
  LOADING = 'loading',           // 加载中
  SUCCESS = 'success',           // 成功/内容态
  ERROR = 'error',              // 错误态
  EMPTY = 'empty',              // 空数据态
  NETWORK_ERROR = 'network_error' // 网络错误态
}

// ====================================================================
// 状态配置接口
// ====================================================================

/**
 * Loading状态配置
 */
export interface LoadingConfig {
  type?: 'spinner' | 'skeleton'  // 显示类型
  text?: string                  // 加载文案
  showText?: boolean             // 是否显示文案
}

/**
 * Error状态配置
 */
export interface ErrorConfig {
  title?: string                 // 错误标题
  message?: string              // 错误描述
  showRetry?: boolean           // 是否显示重试按钮
  retryText?: string            // 重试按钮文案
  icon?: string                 // 错误图标
}

/**
 * Empty状态配置
 */
export interface EmptyConfig {
  title?: string                // 空状态标题
  message?: string             // 空状态描述
  icon?: string                // 空状态图标
  actionText?: string          // 操作按钮文案
  onAction?: () => void        // 操作回调
}

/**
 * 网络状态配置
 */
export interface NetworkConfig {
  enableDetection?: boolean     // 是否启用网络检测
  autoRetry?: boolean          // 网络恢复后是否自动重试
  retryDelay?: number          // 重试延迟（毫秒）
}

// ====================================================================
// 页面状态管理 Composable
// ====================================================================

/**
 * 页面状态管理参数
 */
export interface UsePageStateOptions {
  loading?: boolean | Ref<boolean>
  error?: any | Ref<any>
  data?: any | Ref<any>
  enableNetworkDetection?: boolean
  onRetry?: () => void | Promise<void>
}

/**
 * 页面状态管理 Hook
 * @param options 配置选项
 */
export function usePageState(options: UsePageStateOptions) {
  const {
    loading: loadingProp,
    error: errorProp,
    data: dataProp,
    enableNetworkDetection = true,
    onRetry
  } = options

  // 转换为响应式引用
  const loading = ref(false)
  const error = ref<any>(null)
  const data = ref<any>(null)
  const networkType = ref<string>('')
  const isConnected = ref(true)

  // 同步外部状态
  if (loadingProp !== undefined) {
    if (typeof loadingProp === 'boolean') {
      loading.value = loadingProp
    } else {
      watch(loadingProp, (val) => {
        loading.value = val
      }, { immediate: true })
    }
  }

  if (errorProp !== undefined) {
    if (typeof errorProp === 'object') {
      error.value = errorProp
    } else {
      watch(errorProp, (val) => {
        error.value = val
      }, { immediate: true })
    }
  }

  if (dataProp !== undefined) {
    if (typeof dataProp === 'object') {
      data.value = dataProp
    } else {
      watch(dataProp, (val) => {
        data.value = val
      }, { immediate: true })
    }
  }

  // 网络状态检测
  if (enableNetworkDetection) {
    // 获取当前网络状态
    uni.getNetworkType({
      success: (res) => {
        networkType.value = res.networkType
        isConnected.value = res.networkType !== 'none'
      }
    })

    // 监听网络状态变化
    uni.onNetworkStatusChange((res) => {
      networkType.value = res.networkType
      isConnected.value = res.isConnected
      
      // 网络恢复后自动重试
      if (res.isConnected && error.value && onRetry) {
        setTimeout(() => {
          handleRetry()
        }, 1000)
      }
    })
  }

  // 计算当前页面状态
  const pageState = computed<PageState>(() => {
    // 加载中状态
    if (loading.value) {
      return PageState.LOADING
    }
    
    // 错误状态
    if (error.value) {
      // 检查是否为网络错误
      if (!isConnected.value || isNetworkError(error.value)) {
        return PageState.NETWORK_ERROR
      }
      return PageState.ERROR
    }
    
    // 空数据状态
    if (isEmpty(data.value)) {
      return PageState.EMPTY
    }
    
    // 成功状态
    return PageState.SUCCESS
  })

  // 重试处理
  const handleRetry = async () => {
    if (onRetry) {
      try {
        await onRetry()
      } catch (err) {
        console.error('重试失败:', err)
      }
    }
  }

  return {
    // 状态数据
    pageState,
    loading,
    error,
    data,
    networkType,
    isConnected,
    
    // 方法
    handleRetry,
    
    // 工具方法
    isEmpty: () => isEmpty(data.value),
    isNetworkError: () => isNetworkError(error.value),
  }
}

// ====================================================================
// 工具函数
// ====================================================================

/**
 * 检查是否为网络错误
 * @param error 错误对象
 */
export function isNetworkError(error: any): boolean {
  if (!error) return false
  
  // 检查错误码
  const networkErrorCodes = [
    'NETWORK_ERROR',
    'TIMEOUT',
    'CONNECTION_REFUSED',
    'CONNECTION_TIMEOUT',
    -1, // 网络错误
    408, // 请求超时
    504, // 网关超时
  ]
  
  if (networkErrorCodes.includes(error.code) || networkErrorCodes.includes(error.statusCode)) {
    return true
  }
  
  // 检查错误消息
  const networkErrorMessages = [
    'network error',
    'connection failed',
    'timeout',
    'unable to connect',
    'no internet',
    '网络错误',
    '连接超时',
    '网络连接失败',
    '无网络连接'
  ]
  
  const errorMessage = (error.message || error.errMsg || '').toLowerCase()
  return networkErrorMessages.some(msg => errorMessage.includes(msg))
}

/**
 * 检查数据是否为空
 * @param value 要检查的数据
 */
export function isEmpty(value: any): boolean {
  if (value === null || value === undefined) {
    return true
  }
  
  if (Array.isArray(value)) {
    return value.length === 0
  }
  
  if (typeof value === 'object') {
    return Object.keys(value).length === 0
  }
  
  if (typeof value === 'string') {
    return value.trim() === ''
  }
  
  return false
}

/**
 * 获取错误配置
 * @param error 错误对象
 * @param isNetworkError 是否为网络错误
 */
export function getErrorConfig(error: any, isNetworkError: boolean): ErrorConfig {
  if (isNetworkError) {
    return {
      title: '网络连接失败',
      message: '请检查网络连接后重试',
      icon: 'i-solar:wifi-router-minimalistic-broken',
      showRetry: true,
      retryText: '重新加载'
    }
  }
  
  return {
    title: '加载失败',
    message: error?.message || error?.errMsg || '请求失败，请重试',
    icon: 'i-solar:danger-triangle-broken',
    showRetry: true,
    retryText: '重试'
  }
}

/**
 * 默认配置
 */
export const DEFAULT_LOADING_CONFIG: LoadingConfig = {
  type: 'spinner',
  text: '加载中...',
  showText: true
}

export const DEFAULT_EMPTY_CONFIG: EmptyConfig = {
  title: '暂无数据',
  message: '当前没有相关内容',
  icon: 'i-solar:inbox-out-broken'
}

export const DEFAULT_NETWORK_CONFIG: NetworkConfig = {
  enableDetection: true,
  autoRetry: true,
  retryDelay: 1000
}