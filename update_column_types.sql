-- 直接修改数据库字段类型为smallint(int16)
-- 执行命令: psql -h localhost -U postgres -d bdb -f update_column_types.sql

-- 修改用户表中的Gender字段
ALTER TABLE users ALTER COLUMN gender TYPE smallint;

-- 修改零工表中的相关字段  
ALTER TABLE gigs ALTER COLUMN salary_unit TYPE smallint;
ALTER TABLE gigs ALTER COLUMN settlement TYPE smallint;
ALTER TABLE gigs ALTER COLUMN gender TYPE smallint;
ALTER TABLE gigs ALTER COLUMN experience TYPE smallint;
ALTER TABLE gigs ALTER COLUMN education TYPE smallint;

-- 修改职位表中的相关字段
ALTER TABLE jobs ALTER COLUMN experience_req TYPE smallint;
ALTER TABLE jobs ALTER COLUMN education_req TYPE smallint; 
ALTER TABLE jobs ALTER COLUMN work_type TYPE smallint;

-- 修改简历表中的Gender字段
ALTER TABLE resumes ALTER COLUMN gender TYPE smallint;

-- 修改企业表中的CompanySize字段
ALTER TABLE enterprises ALTER COLUMN company_size TYPE smallint;

-- 显示修改后的字段信息
SELECT 
    table_name, 
    column_name, 
    data_type 
FROM information_schema.columns 
WHERE table_name IN ('users', 'gigs', 'jobs', 'resumes', 'enterprises') 
    AND column_name IN ('gender', 'experience_req', 'education_req', 'work_type', 'salary_unit', 'settlement', 'experience', 'education', 'company_size')
ORDER BY table_name, column_name;